"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObservableSitePagesApi = exports.ObservableLandingPagesApi = void 0;
const rxjsStub_1 = require("../rxjsStub");
const rxjsStub_2 = require("../rxjsStub");
const LandingPagesApi_1 = require("../apis/LandingPagesApi");
class ObservableLandingPagesApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.configuration = configuration;
        this.requestFactory = requestFactory || new LandingPagesApi_1.LandingPagesApiRequestFactory(configuration);
        this.responseProcessor = responseProcessor || new LandingPagesApi_1.LandingPagesApiResponseProcessor();
    }
    archiveWithHttpInfo(objectId, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.archive(objectId, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.archiveWithHttpInfo(rsp)));
        }));
    }
    archive(objectId, archived, _options) {
        return this.archiveWithHttpInfo(objectId, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    archiveBatchWithHttpInfo(batchInputString, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.archiveBatch(batchInputString, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.archiveBatchWithHttpInfo(rsp)));
        }));
    }
    archiveBatch(batchInputString, _options) {
        return this.archiveBatchWithHttpInfo(batchInputString, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    archiveFolderWithHttpInfo(objectId, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.archiveFolder(objectId, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.archiveFolderWithHttpInfo(rsp)));
        }));
    }
    archiveFolder(objectId, archived, _options) {
        return this.archiveFolderWithHttpInfo(objectId, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    archiveFoldersWithHttpInfo(batchInputString, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.archiveFolders(batchInputString, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.archiveFoldersWithHttpInfo(rsp)));
        }));
    }
    archiveFolders(batchInputString, _options) {
        return this.archiveFoldersWithHttpInfo(batchInputString, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.attachToLangGroup(attachToLangPrimaryRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.attachToLangGroupWithHttpInfo(rsp)));
        }));
    }
    attachToLangGroup(attachToLangPrimaryRequestVNext, _options) {
        return this.attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    cloneWithHttpInfo(contentCloneRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.clone(contentCloneRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.cloneWithHttpInfo(rsp)));
        }));
    }
    clone(contentCloneRequestVNext, _options) {
        return this.cloneWithHttpInfo(contentCloneRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createWithHttpInfo(page, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.create(page, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createWithHttpInfo(rsp)));
        }));
    }
    create(page, _options) {
        return this.createWithHttpInfo(page, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createABTestVariationWithHttpInfo(abTestCreateRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.createABTestVariation(abTestCreateRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createABTestVariationWithHttpInfo(rsp)));
        }));
    }
    createABTestVariation(abTestCreateRequestVNext, _options) {
        return this.createABTestVariationWithHttpInfo(abTestCreateRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createBatchWithHttpInfo(batchInputPage, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.createBatch(batchInputPage, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createBatchWithHttpInfo(rsp)));
        }));
    }
    createBatch(batchInputPage, _options) {
        return this.createBatchWithHttpInfo(batchInputPage, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createFolderWithHttpInfo(contentFolder, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.createFolder(contentFolder, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createFolderWithHttpInfo(rsp)));
        }));
    }
    createFolder(contentFolder, _options) {
        return this.createFolderWithHttpInfo(contentFolder, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createFoldersWithHttpInfo(batchInputContentFolder, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.createFolders(batchInputContentFolder, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createFoldersWithHttpInfo(rsp)));
        }));
    }
    createFolders(batchInputContentFolder, _options) {
        return this.createFoldersWithHttpInfo(batchInputContentFolder, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.createLangVariation(contentLanguageCloneRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createLangVariationWithHttpInfo(rsp)));
        }));
    }
    createLangVariation(contentLanguageCloneRequestVNext, _options) {
        return this.createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.detachFromLangGroup(detachFromLangGroupRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.detachFromLangGroupWithHttpInfo(rsp)));
        }));
    }
    detachFromLangGroup(detachFromLangGroupRequestVNext, _options) {
        return this.detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    endActiveABTestWithHttpInfo(abTestEndRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.endActiveABTest(abTestEndRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.endActiveABTestWithHttpInfo(rsp)));
        }));
    }
    endActiveABTest(abTestEndRequestVNext, _options) {
        return this.endActiveABTestWithHttpInfo(abTestEndRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getByIdWithHttpInfo(objectId, archived, property, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getById(objectId, archived, property, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getByIdWithHttpInfo(rsp)));
        }));
    }
    getById(objectId, archived, property, _options) {
        return this.getByIdWithHttpInfo(objectId, archived, property, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getDraftByIdWithHttpInfo(objectId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getDraftById(objectId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getDraftByIdWithHttpInfo(rsp)));
        }));
    }
    getDraftById(objectId, _options) {
        return this.getDraftByIdWithHttpInfo(objectId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getFolderByIdWithHttpInfo(objectId, archived, property, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getFolderById(objectId, archived, property, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getFolderByIdWithHttpInfo(rsp)));
        }));
    }
    getFolderById(objectId, archived, property, _options) {
        return this.getFolderByIdWithHttpInfo(objectId, archived, property, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getFolderPreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getFolderPreviousVersion(objectId, revisionId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getFolderPreviousVersionWithHttpInfo(rsp)));
        }));
    }
    getFolderPreviousVersion(objectId, revisionId, _options) {
        return this.getFolderPreviousVersionWithHttpInfo(objectId, revisionId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getFolderPreviousVersionsWithHttpInfo(objectId, after, before, limit, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getFolderPreviousVersions(objectId, after, before, limit, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getFolderPreviousVersionsWithHttpInfo(rsp)));
        }));
    }
    getFolderPreviousVersions(objectId, after, before, limit, _options) {
        return this.getFolderPreviousVersionsWithHttpInfo(objectId, after, before, limit, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getFoldersPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getFoldersPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getFoldersPageWithHttpInfo(rsp)));
        }));
    }
    getFoldersPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        return this.getFoldersPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getPageWithHttpInfo(rsp)));
        }));
    }
    getPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        return this.getPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getPreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getPreviousVersion(objectId, revisionId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getPreviousVersionWithHttpInfo(rsp)));
        }));
    }
    getPreviousVersion(objectId, revisionId, _options) {
        return this.getPreviousVersionWithHttpInfo(objectId, revisionId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getPreviousVersionsWithHttpInfo(objectId, after, before, limit, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getPreviousVersions(objectId, after, before, limit, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getPreviousVersionsWithHttpInfo(rsp)));
        }));
    }
    getPreviousVersions(objectId, after, before, limit, _options) {
        return this.getPreviousVersionsWithHttpInfo(objectId, after, before, limit, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    pushLiveWithHttpInfo(objectId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.pushLive(objectId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.pushLiveWithHttpInfo(rsp)));
        }));
    }
    pushLive(objectId, _options) {
        return this.pushLiveWithHttpInfo(objectId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    readBatchWithHttpInfo(batchInputString, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.readBatch(batchInputString, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.readBatchWithHttpInfo(rsp)));
        }));
    }
    readBatch(batchInputString, archived, _options) {
        return this.readBatchWithHttpInfo(batchInputString, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    readFoldersWithHttpInfo(batchInputString, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.readFolders(batchInputString, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.readFoldersWithHttpInfo(rsp)));
        }));
    }
    readFolders(batchInputString, archived, _options) {
        return this.readFoldersWithHttpInfo(batchInputString, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.rerunPreviousABTest(abTestRerunRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.rerunPreviousABTestWithHttpInfo(rsp)));
        }));
    }
    rerunPreviousABTest(abTestRerunRequestVNext, _options) {
        return this.rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    resetDraftWithHttpInfo(objectId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.resetDraft(objectId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.resetDraftWithHttpInfo(rsp)));
        }));
    }
    resetDraft(objectId, _options) {
        return this.resetDraftWithHttpInfo(objectId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    restoreFolderPreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.restoreFolderPreviousVersion(objectId, revisionId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.restoreFolderPreviousVersionWithHttpInfo(rsp)));
        }));
    }
    restoreFolderPreviousVersion(objectId, revisionId, _options) {
        return this.restoreFolderPreviousVersionWithHttpInfo(objectId, revisionId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    restorePreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.restorePreviousVersion(objectId, revisionId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.restorePreviousVersionWithHttpInfo(rsp)));
        }));
    }
    restorePreviousVersion(objectId, revisionId, _options) {
        return this.restorePreviousVersionWithHttpInfo(objectId, revisionId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    restorePreviousVersionToDraftWithHttpInfo(objectId, revisionId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.restorePreviousVersionToDraft(objectId, revisionId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.restorePreviousVersionToDraftWithHttpInfo(rsp)));
        }));
    }
    restorePreviousVersionToDraft(objectId, revisionId, _options) {
        return this.restorePreviousVersionToDraftWithHttpInfo(objectId, revisionId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    scheduleWithHttpInfo(contentScheduleRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.schedule(contentScheduleRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.scheduleWithHttpInfo(rsp)));
        }));
    }
    schedule(contentScheduleRequestVNext, _options) {
        return this.scheduleWithHttpInfo(contentScheduleRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.setLangPrimary(setNewLanguagePrimaryRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.setLangPrimaryWithHttpInfo(rsp)));
        }));
    }
    setLangPrimary(setNewLanguagePrimaryRequestVNext, _options) {
        return this.setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateWithHttpInfo(objectId, page, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.update(objectId, page, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateWithHttpInfo(rsp)));
        }));
    }
    update(objectId, page, archived, _options) {
        return this.updateWithHttpInfo(objectId, page, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateBatchWithHttpInfo(batchInputJsonNode, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.updateBatch(batchInputJsonNode, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateBatchWithHttpInfo(rsp)));
        }));
    }
    updateBatch(batchInputJsonNode, archived, _options) {
        return this.updateBatchWithHttpInfo(batchInputJsonNode, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateDraftWithHttpInfo(objectId, page, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.updateDraft(objectId, page, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateDraftWithHttpInfo(rsp)));
        }));
    }
    updateDraft(objectId, page, _options) {
        return this.updateDraftWithHttpInfo(objectId, page, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateFolderWithHttpInfo(objectId, contentFolder, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.updateFolder(objectId, contentFolder, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateFolderWithHttpInfo(rsp)));
        }));
    }
    updateFolder(objectId, contentFolder, archived, _options) {
        return this.updateFolderWithHttpInfo(objectId, contentFolder, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateFoldersWithHttpInfo(batchInputJsonNode, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.updateFolders(batchInputJsonNode, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateFoldersWithHttpInfo(rsp)));
        }));
    }
    updateFolders(batchInputJsonNode, archived, _options) {
        return this.updateFoldersWithHttpInfo(batchInputJsonNode, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateLangsWithHttpInfo(updateLanguagesRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.updateLangs(updateLanguagesRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateLangsWithHttpInfo(rsp)));
        }));
    }
    updateLangs(updateLanguagesRequestVNext, _options) {
        return this.updateLangsWithHttpInfo(updateLanguagesRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
}
exports.ObservableLandingPagesApi = ObservableLandingPagesApi;
const SitePagesApi_1 = require("../apis/SitePagesApi");
class ObservableSitePagesApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.configuration = configuration;
        this.requestFactory = requestFactory || new SitePagesApi_1.SitePagesApiRequestFactory(configuration);
        this.responseProcessor = responseProcessor || new SitePagesApi_1.SitePagesApiResponseProcessor();
    }
    archiveWithHttpInfo(objectId, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.archive(objectId, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.archiveWithHttpInfo(rsp)));
        }));
    }
    archive(objectId, archived, _options) {
        return this.archiveWithHttpInfo(objectId, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    archiveBatchWithHttpInfo(batchInputString, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.archiveBatch(batchInputString, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.archiveBatchWithHttpInfo(rsp)));
        }));
    }
    archiveBatch(batchInputString, _options) {
        return this.archiveBatchWithHttpInfo(batchInputString, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.attachToLangGroup(attachToLangPrimaryRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.attachToLangGroupWithHttpInfo(rsp)));
        }));
    }
    attachToLangGroup(attachToLangPrimaryRequestVNext, _options) {
        return this.attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    cloneWithHttpInfo(contentCloneRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.clone(contentCloneRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.cloneWithHttpInfo(rsp)));
        }));
    }
    clone(contentCloneRequestVNext, _options) {
        return this.cloneWithHttpInfo(contentCloneRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createWithHttpInfo(page, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.create(page, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createWithHttpInfo(rsp)));
        }));
    }
    create(page, _options) {
        return this.createWithHttpInfo(page, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createABTestVariationWithHttpInfo(abTestCreateRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.createABTestVariation(abTestCreateRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createABTestVariationWithHttpInfo(rsp)));
        }));
    }
    createABTestVariation(abTestCreateRequestVNext, _options) {
        return this.createABTestVariationWithHttpInfo(abTestCreateRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createBatchWithHttpInfo(batchInputPage, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.createBatch(batchInputPage, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createBatchWithHttpInfo(rsp)));
        }));
    }
    createBatch(batchInputPage, _options) {
        return this.createBatchWithHttpInfo(batchInputPage, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.createLangVariation(contentLanguageCloneRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createLangVariationWithHttpInfo(rsp)));
        }));
    }
    createLangVariation(contentLanguageCloneRequestVNext, _options) {
        return this.createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.detachFromLangGroup(detachFromLangGroupRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.detachFromLangGroupWithHttpInfo(rsp)));
        }));
    }
    detachFromLangGroup(detachFromLangGroupRequestVNext, _options) {
        return this.detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    endActiveABTestWithHttpInfo(abTestEndRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.endActiveABTest(abTestEndRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.endActiveABTestWithHttpInfo(rsp)));
        }));
    }
    endActiveABTest(abTestEndRequestVNext, _options) {
        return this.endActiveABTestWithHttpInfo(abTestEndRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getByIdWithHttpInfo(objectId, archived, property, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getById(objectId, archived, property, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getByIdWithHttpInfo(rsp)));
        }));
    }
    getById(objectId, archived, property, _options) {
        return this.getByIdWithHttpInfo(objectId, archived, property, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getDraftByIdWithHttpInfo(objectId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getDraftById(objectId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getDraftByIdWithHttpInfo(rsp)));
        }));
    }
    getDraftById(objectId, _options) {
        return this.getDraftByIdWithHttpInfo(objectId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getPageWithHttpInfo(rsp)));
        }));
    }
    getPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        return this.getPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getPreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getPreviousVersion(objectId, revisionId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getPreviousVersionWithHttpInfo(rsp)));
        }));
    }
    getPreviousVersion(objectId, revisionId, _options) {
        return this.getPreviousVersionWithHttpInfo(objectId, revisionId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getPreviousVersionsWithHttpInfo(objectId, after, before, limit, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getPreviousVersions(objectId, after, before, limit, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getPreviousVersionsWithHttpInfo(rsp)));
        }));
    }
    getPreviousVersions(objectId, after, before, limit, _options) {
        return this.getPreviousVersionsWithHttpInfo(objectId, after, before, limit, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    pushLiveWithHttpInfo(objectId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.pushLive(objectId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.pushLiveWithHttpInfo(rsp)));
        }));
    }
    pushLive(objectId, _options) {
        return this.pushLiveWithHttpInfo(objectId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    readBatchWithHttpInfo(batchInputString, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.readBatch(batchInputString, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.readBatchWithHttpInfo(rsp)));
        }));
    }
    readBatch(batchInputString, archived, _options) {
        return this.readBatchWithHttpInfo(batchInputString, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.rerunPreviousABTest(abTestRerunRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.rerunPreviousABTestWithHttpInfo(rsp)));
        }));
    }
    rerunPreviousABTest(abTestRerunRequestVNext, _options) {
        return this.rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    resetDraftWithHttpInfo(objectId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.resetDraft(objectId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.resetDraftWithHttpInfo(rsp)));
        }));
    }
    resetDraft(objectId, _options) {
        return this.resetDraftWithHttpInfo(objectId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    restorePreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.restorePreviousVersion(objectId, revisionId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.restorePreviousVersionWithHttpInfo(rsp)));
        }));
    }
    restorePreviousVersion(objectId, revisionId, _options) {
        return this.restorePreviousVersionWithHttpInfo(objectId, revisionId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    restorePreviousVersionToDraftWithHttpInfo(objectId, revisionId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.restorePreviousVersionToDraft(objectId, revisionId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.restorePreviousVersionToDraftWithHttpInfo(rsp)));
        }));
    }
    restorePreviousVersionToDraft(objectId, revisionId, _options) {
        return this.restorePreviousVersionToDraftWithHttpInfo(objectId, revisionId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    scheduleWithHttpInfo(contentScheduleRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.schedule(contentScheduleRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.scheduleWithHttpInfo(rsp)));
        }));
    }
    schedule(contentScheduleRequestVNext, _options) {
        return this.scheduleWithHttpInfo(contentScheduleRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.setLangPrimary(setNewLanguagePrimaryRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.setLangPrimaryWithHttpInfo(rsp)));
        }));
    }
    setLangPrimary(setNewLanguagePrimaryRequestVNext, _options) {
        return this.setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateWithHttpInfo(objectId, page, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.update(objectId, page, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateWithHttpInfo(rsp)));
        }));
    }
    update(objectId, page, archived, _options) {
        return this.updateWithHttpInfo(objectId, page, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateBatchWithHttpInfo(batchInputJsonNode, archived, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.updateBatch(batchInputJsonNode, archived, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateBatchWithHttpInfo(rsp)));
        }));
    }
    updateBatch(batchInputJsonNode, archived, _options) {
        return this.updateBatchWithHttpInfo(batchInputJsonNode, archived, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateDraftWithHttpInfo(objectId, page, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.updateDraft(objectId, page, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateDraftWithHttpInfo(rsp)));
        }));
    }
    updateDraft(objectId, page, _options) {
        return this.updateDraftWithHttpInfo(objectId, page, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    updateLangsWithHttpInfo(updateLanguagesRequestVNext, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.updateLangs(updateLanguagesRequestVNext, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.updateLangsWithHttpInfo(rsp)));
        }));
    }
    updateLangs(updateLanguagesRequestVNext, _options) {
        return this.updateLangsWithHttpInfo(updateLanguagesRequestVNext, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
}
exports.ObservableSitePagesApi = ObservableSitePagesApi;
//# sourceMappingURL=ObservableAPI.js.map