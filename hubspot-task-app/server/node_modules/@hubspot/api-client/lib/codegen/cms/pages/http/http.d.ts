/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import FormData from "form-data";
import { URLSearchParams } from 'url';
import * as http from 'http';
import * as https from 'https';
import { Observable } from '../rxjsStub';
export * from './isomorphic-fetch';
export declare enum HttpMethod {
    GET = "GET",
    HEAD = "HEAD",
    POST = "POST",
    PUT = "PUT",
    DELETE = "DELETE",
    CONNECT = "CONNECT",
    OPTIONS = "OPTIONS",
    TRACE = "TRACE",
    PATCH = "PATCH"
}
export type HttpFile = {
    data: Buffer;
    name: string;
};
export declare class HttpException extends Error {
    constructor(msg: string);
}
export type RequestBody = undefined | string | FormData | URLSearchParams;
type Headers = Record<string, string>;
export declare class RequestContext {
    private httpMethod;
    private headers;
    private body;
    private url;
    private agent;
    constructor(url: string, httpMethod: HttpMethod);
    getUrl(): string;
    setUrl(url: string): void;
    setBody(body: RequestBody): void;
    getHttpMethod(): HttpMethod;
    getHeaders(): Headers;
    getBody(): RequestBody;
    setQueryParam(name: string, value: string): void;
    appendQueryParam(name: string, value: string): void;
    addCookie(name: string, value: string): void;
    setHeaderParam(key: string, value: string): void;
    setAgent(agent: http.Agent | https.Agent): void;
    getAgent(): http.Agent | https.Agent | undefined;
}
export interface ResponseBody {
    text(): Promise<string>;
    binary(): Promise<Buffer>;
}
export declare class SelfDecodingBody implements ResponseBody {
    private dataSource;
    constructor(dataSource: Promise<Buffer>);
    binary(): Promise<Buffer>;
    text(): Promise<string>;
}
export declare class ResponseContext {
    httpStatusCode: number;
    headers: Headers;
    body: ResponseBody;
    constructor(httpStatusCode: number, headers: Headers, body: ResponseBody);
    getParsedHeader(headerName: string): Headers;
    getBodyAsFile(): Promise<HttpFile>;
    getBodyAsAny(): Promise<string | Buffer | undefined>;
}
export interface HttpLibrary {
    send(request: RequestContext): Observable<ResponseContext>;
}
export interface PromiseHttpLibrary {
    send(request: RequestContext): Promise<ResponseContext>;
}
export declare function wrapHttpLibrary(promiseHttpLibrary: PromiseHttpLibrary): HttpLibrary;
export declare class HttpInfo<T> extends ResponseContext {
    data: T;
    constructor(httpStatusCode: number, headers: Headers, body: ResponseBody, data: T);
}
