"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActionResponseStatusEnum = exports.ActionResponse = void 0;
class ActionResponse {
    static getAttributeTypeMap() {
        return ActionResponse.attributeTypeMap;
    }
    constructor() {
    }
}
exports.ActionResponse = ActionResponse;
ActionResponse.discriminator = undefined;
ActionResponse.mapping = undefined;
ActionResponse.attributeTypeMap = [
    {
        "name": "completedAt",
        "baseName": "completedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "requestedAt",
        "baseName": "requestedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "startedAt",
        "baseName": "startedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "links",
        "baseName": "links",
        "type": "{ [key: string]: string; }",
        "format": ""
    },
    {
        "name": "status",
        "baseName": "status",
        "type": "ActionResponseStatusEnum",
        "format": ""
    }
];
var ActionResponseStatusEnum;
(function (ActionResponseStatusEnum) {
    ActionResponseStatusEnum["Pending"] = "PENDING";
    ActionResponseStatusEnum["Processing"] = "PROCESSING";
    ActionResponseStatusEnum["Canceled"] = "CANCELED";
    ActionResponseStatusEnum["Complete"] = "COMPLETE";
})(ActionResponseStatusEnum = exports.ActionResponseStatusEnum || (exports.ActionResponseStatusEnum = {}));
//# sourceMappingURL=ActionResponse.js.map