"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchResponsePageStatusEnum = exports.BatchResponsePage = void 0;
class BatchResponsePage {
    static getAttributeTypeMap() {
        return BatchResponsePage.attributeTypeMap;
    }
    constructor() {
    }
}
exports.BatchResponsePage = BatchResponsePage;
BatchResponsePage.discriminator = undefined;
BatchResponsePage.mapping = undefined;
BatchResponsePage.attributeTypeMap = [
    {
        "name": "completedAt",
        "baseName": "completedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "requestedAt",
        "baseName": "requestedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "startedAt",
        "baseName": "startedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "links",
        "baseName": "links",
        "type": "{ [key: string]: string; }",
        "format": ""
    },
    {
        "name": "results",
        "baseName": "results",
        "type": "Array<Page>",
        "format": ""
    },
    {
        "name": "status",
        "baseName": "status",
        "type": "BatchResponsePageStatusEnum",
        "format": ""
    }
];
var BatchResponsePageStatusEnum;
(function (BatchResponsePageStatusEnum) {
    BatchResponsePageStatusEnum["Pending"] = "PENDING";
    BatchResponsePageStatusEnum["Processing"] = "PROCESSING";
    BatchResponsePageStatusEnum["Canceled"] = "CANCELED";
    BatchResponsePageStatusEnum["Complete"] = "COMPLETE";
})(BatchResponsePageStatusEnum = exports.BatchResponsePageStatusEnum || (exports.BatchResponsePageStatusEnum = {}));
//# sourceMappingURL=BatchResponsePage.js.map