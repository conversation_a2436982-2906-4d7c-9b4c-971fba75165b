import { HttpInfo } from '../http/http';
import { Configuration, ConfigurationOptions } from '../configuration';
import { AbTestCreateRequestVNext } from '../models/AbTestCreateRequestVNext';
import { AbTestEndRequestVNext } from '../models/AbTestEndRequestVNext';
import { AbTestRerunRequestVNext } from '../models/AbTestRerunRequestVNext';
import { AttachToLangPrimaryRequestVNext } from '../models/AttachToLangPrimaryRequestVNext';
import { BatchInputContentFolder } from '../models/BatchInputContentFolder';
import { BatchInputJsonNode } from '../models/BatchInputJsonNode';
import { BatchInputPage } from '../models/BatchInputPage';
import { BatchInputString } from '../models/BatchInputString';
import { BatchResponseContentFolder } from '../models/BatchResponseContentFolder';
import { BatchResponseContentFolderWithErrors } from '../models/BatchResponseContentFolderWithErrors';
import { BatchResponsePage } from '../models/BatchResponsePage';
import { BatchResponsePageWithErrors } from '../models/BatchResponsePageWithErrors';
import { CollectionResponseWithTotalContentFolderForwardPaging } from '../models/CollectionResponseWithTotalContentFolderForwardPaging';
import { CollectionResponseWithTotalPageForwardPaging } from '../models/CollectionResponseWithTotalPageForwardPaging';
import { CollectionResponseWithTotalVersionContentFolder } from '../models/CollectionResponseWithTotalVersionContentFolder';
import { CollectionResponseWithTotalVersionPage } from '../models/CollectionResponseWithTotalVersionPage';
import { ContentCloneRequestVNext } from '../models/ContentCloneRequestVNext';
import { ContentFolder } from '../models/ContentFolder';
import { ContentLanguageCloneRequestVNext } from '../models/ContentLanguageCloneRequestVNext';
import { ContentScheduleRequestVNext } from '../models/ContentScheduleRequestVNext';
import { DetachFromLangGroupRequestVNext } from '../models/DetachFromLangGroupRequestVNext';
import { Page } from '../models/Page';
import { SetNewLanguagePrimaryRequestVNext } from '../models/SetNewLanguagePrimaryRequestVNext';
import { UpdateLanguagesRequestVNext } from '../models/UpdateLanguagesRequestVNext';
import { VersionContentFolder } from '../models/VersionContentFolder';
import { VersionPage } from '../models/VersionPage';
import { LandingPagesApiRequestFactory, LandingPagesApiResponseProcessor } from "../apis/LandingPagesApi";
export interface LandingPagesApiArchiveRequest {
    objectId: string;
    archived?: boolean;
}
export interface LandingPagesApiArchiveBatchRequest {
    batchInputString: BatchInputString;
}
export interface LandingPagesApiArchiveFolderRequest {
    objectId: string;
    archived?: boolean;
}
export interface LandingPagesApiArchiveFoldersRequest {
    batchInputString: BatchInputString;
}
export interface LandingPagesApiAttachToLangGroupRequest {
    attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext;
}
export interface LandingPagesApiCloneRequest {
    contentCloneRequestVNext: ContentCloneRequestVNext;
}
export interface LandingPagesApiCreateRequest {
    page: Page;
}
export interface LandingPagesApiCreateABTestVariationRequest {
    abTestCreateRequestVNext: AbTestCreateRequestVNext;
}
export interface LandingPagesApiCreateBatchRequest {
    batchInputPage: BatchInputPage;
}
export interface LandingPagesApiCreateFolderRequest {
    contentFolder: ContentFolder;
}
export interface LandingPagesApiCreateFoldersRequest {
    batchInputContentFolder: BatchInputContentFolder;
}
export interface LandingPagesApiCreateLangVariationRequest {
    contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext;
}
export interface LandingPagesApiDetachFromLangGroupRequest {
    detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext;
}
export interface LandingPagesApiEndActiveABTestRequest {
    abTestEndRequestVNext: AbTestEndRequestVNext;
}
export interface LandingPagesApiGetByIdRequest {
    objectId: string;
    archived?: boolean;
    property?: string;
}
export interface LandingPagesApiGetDraftByIdRequest {
    objectId: string;
}
export interface LandingPagesApiGetFolderByIdRequest {
    objectId: string;
    archived?: boolean;
    property?: string;
}
export interface LandingPagesApiGetFolderPreviousVersionRequest {
    objectId: string;
    revisionId: string;
}
export interface LandingPagesApiGetFolderPreviousVersionsRequest {
    objectId: string;
    after?: string;
    before?: string;
    limit?: number;
}
export interface LandingPagesApiGetFoldersPageRequest {
    createdAt?: Date;
    createdAfter?: Date;
    createdBefore?: Date;
    updatedAt?: Date;
    updatedAfter?: Date;
    updatedBefore?: Date;
    sort?: Array<string>;
    after?: string;
    limit?: number;
    archived?: boolean;
    property?: string;
}
export interface LandingPagesApiGetPageRequest {
    createdAt?: Date;
    createdAfter?: Date;
    createdBefore?: Date;
    updatedAt?: Date;
    updatedAfter?: Date;
    updatedBefore?: Date;
    sort?: Array<string>;
    after?: string;
    limit?: number;
    archived?: boolean;
    property?: string;
}
export interface LandingPagesApiGetPreviousVersionRequest {
    objectId: string;
    revisionId: string;
}
export interface LandingPagesApiGetPreviousVersionsRequest {
    objectId: string;
    after?: string;
    before?: string;
    limit?: number;
}
export interface LandingPagesApiPushLiveRequest {
    objectId: string;
}
export interface LandingPagesApiReadBatchRequest {
    batchInputString: BatchInputString;
    archived?: boolean;
}
export interface LandingPagesApiReadFoldersRequest {
    batchInputString: BatchInputString;
    archived?: boolean;
}
export interface LandingPagesApiRerunPreviousABTestRequest {
    abTestRerunRequestVNext: AbTestRerunRequestVNext;
}
export interface LandingPagesApiResetDraftRequest {
    objectId: string;
}
export interface LandingPagesApiRestoreFolderPreviousVersionRequest {
    objectId: string;
    revisionId: string;
}
export interface LandingPagesApiRestorePreviousVersionRequest {
    objectId: string;
    revisionId: string;
}
export interface LandingPagesApiRestorePreviousVersionToDraftRequest {
    objectId: string;
    revisionId: number;
}
export interface LandingPagesApiScheduleRequest {
    contentScheduleRequestVNext: ContentScheduleRequestVNext;
}
export interface LandingPagesApiSetLangPrimaryRequest {
    setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext;
}
export interface LandingPagesApiUpdateRequest {
    objectId: string;
    page: Page;
    archived?: boolean;
}
export interface LandingPagesApiUpdateBatchRequest {
    batchInputJsonNode: BatchInputJsonNode;
    archived?: boolean;
}
export interface LandingPagesApiUpdateDraftRequest {
    objectId: string;
    page: Page;
}
export interface LandingPagesApiUpdateFolderRequest {
    objectId: string;
    contentFolder: ContentFolder;
    archived?: boolean;
}
export interface LandingPagesApiUpdateFoldersRequest {
    batchInputJsonNode: BatchInputJsonNode;
    archived?: boolean;
}
export interface LandingPagesApiUpdateLangsRequest {
    updateLanguagesRequestVNext: UpdateLanguagesRequestVNext;
}
export declare class ObjectLandingPagesApi {
    private api;
    constructor(configuration: Configuration, requestFactory?: LandingPagesApiRequestFactory, responseProcessor?: LandingPagesApiResponseProcessor);
    archiveWithHttpInfo(param: LandingPagesApiArchiveRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    archive(param: LandingPagesApiArchiveRequest, options?: ConfigurationOptions): Promise<void>;
    archiveBatchWithHttpInfo(param: LandingPagesApiArchiveBatchRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    archiveBatch(param: LandingPagesApiArchiveBatchRequest, options?: ConfigurationOptions): Promise<void>;
    archiveFolderWithHttpInfo(param: LandingPagesApiArchiveFolderRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    archiveFolder(param: LandingPagesApiArchiveFolderRequest, options?: ConfigurationOptions): Promise<void>;
    archiveFoldersWithHttpInfo(param: LandingPagesApiArchiveFoldersRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    archiveFolders(param: LandingPagesApiArchiveFoldersRequest, options?: ConfigurationOptions): Promise<void>;
    attachToLangGroupWithHttpInfo(param: LandingPagesApiAttachToLangGroupRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    attachToLangGroup(param: LandingPagesApiAttachToLangGroupRequest, options?: ConfigurationOptions): Promise<void>;
    cloneWithHttpInfo(param: LandingPagesApiCloneRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    clone(param: LandingPagesApiCloneRequest, options?: ConfigurationOptions): Promise<Page>;
    createWithHttpInfo(param: LandingPagesApiCreateRequest, options?: ConfigurationOptions): Promise<HttpInfo<void | Page>>;
    create(param: LandingPagesApiCreateRequest, options?: ConfigurationOptions): Promise<void | Page>;
    createABTestVariationWithHttpInfo(param: LandingPagesApiCreateABTestVariationRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    createABTestVariation(param: LandingPagesApiCreateABTestVariationRequest, options?: ConfigurationOptions): Promise<Page>;
    createBatchWithHttpInfo(param: LandingPagesApiCreateBatchRequest, options?: ConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    createBatch(param: LandingPagesApiCreateBatchRequest, options?: ConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    createFolderWithHttpInfo(param: LandingPagesApiCreateFolderRequest, options?: ConfigurationOptions): Promise<HttpInfo<ContentFolder>>;
    createFolder(param: LandingPagesApiCreateFolderRequest, options?: ConfigurationOptions): Promise<ContentFolder>;
    createFoldersWithHttpInfo(param: LandingPagesApiCreateFoldersRequest, options?: ConfigurationOptions): Promise<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    createFolders(param: LandingPagesApiCreateFoldersRequest, options?: ConfigurationOptions): Promise<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>;
    createLangVariationWithHttpInfo(param: LandingPagesApiCreateLangVariationRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    createLangVariation(param: LandingPagesApiCreateLangVariationRequest, options?: ConfigurationOptions): Promise<Page>;
    detachFromLangGroupWithHttpInfo(param: LandingPagesApiDetachFromLangGroupRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    detachFromLangGroup(param: LandingPagesApiDetachFromLangGroupRequest, options?: ConfigurationOptions): Promise<void>;
    endActiveABTestWithHttpInfo(param: LandingPagesApiEndActiveABTestRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    endActiveABTest(param: LandingPagesApiEndActiveABTestRequest, options?: ConfigurationOptions): Promise<void>;
    getByIdWithHttpInfo(param: LandingPagesApiGetByIdRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    getById(param: LandingPagesApiGetByIdRequest, options?: ConfigurationOptions): Promise<Page>;
    getDraftByIdWithHttpInfo(param: LandingPagesApiGetDraftByIdRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    getDraftById(param: LandingPagesApiGetDraftByIdRequest, options?: ConfigurationOptions): Promise<Page>;
    getFolderByIdWithHttpInfo(param: LandingPagesApiGetFolderByIdRequest, options?: ConfigurationOptions): Promise<HttpInfo<ContentFolder>>;
    getFolderById(param: LandingPagesApiGetFolderByIdRequest, options?: ConfigurationOptions): Promise<ContentFolder>;
    getFolderPreviousVersionWithHttpInfo(param: LandingPagesApiGetFolderPreviousVersionRequest, options?: ConfigurationOptions): Promise<HttpInfo<VersionContentFolder>>;
    getFolderPreviousVersion(param: LandingPagesApiGetFolderPreviousVersionRequest, options?: ConfigurationOptions): Promise<VersionContentFolder>;
    getFolderPreviousVersionsWithHttpInfo(param: LandingPagesApiGetFolderPreviousVersionsRequest, options?: ConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalVersionContentFolder>>;
    getFolderPreviousVersions(param: LandingPagesApiGetFolderPreviousVersionsRequest, options?: ConfigurationOptions): Promise<CollectionResponseWithTotalVersionContentFolder>;
    getFoldersPageWithHttpInfo(param?: LandingPagesApiGetFoldersPageRequest, options?: ConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalContentFolderForwardPaging>>;
    getFoldersPage(param?: LandingPagesApiGetFoldersPageRequest, options?: ConfigurationOptions): Promise<CollectionResponseWithTotalContentFolderForwardPaging>;
    getPageWithHttpInfo(param?: LandingPagesApiGetPageRequest, options?: ConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalPageForwardPaging>>;
    getPage(param?: LandingPagesApiGetPageRequest, options?: ConfigurationOptions): Promise<CollectionResponseWithTotalPageForwardPaging>;
    getPreviousVersionWithHttpInfo(param: LandingPagesApiGetPreviousVersionRequest, options?: ConfigurationOptions): Promise<HttpInfo<VersionPage>>;
    getPreviousVersion(param: LandingPagesApiGetPreviousVersionRequest, options?: ConfigurationOptions): Promise<VersionPage>;
    getPreviousVersionsWithHttpInfo(param: LandingPagesApiGetPreviousVersionsRequest, options?: ConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalVersionPage>>;
    getPreviousVersions(param: LandingPagesApiGetPreviousVersionsRequest, options?: ConfigurationOptions): Promise<CollectionResponseWithTotalVersionPage>;
    pushLiveWithHttpInfo(param: LandingPagesApiPushLiveRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    pushLive(param: LandingPagesApiPushLiveRequest, options?: ConfigurationOptions): Promise<void>;
    readBatchWithHttpInfo(param: LandingPagesApiReadBatchRequest, options?: ConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    readBatch(param: LandingPagesApiReadBatchRequest, options?: ConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    readFoldersWithHttpInfo(param: LandingPagesApiReadFoldersRequest, options?: ConfigurationOptions): Promise<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    readFolders(param: LandingPagesApiReadFoldersRequest, options?: ConfigurationOptions): Promise<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>;
    rerunPreviousABTestWithHttpInfo(param: LandingPagesApiRerunPreviousABTestRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    rerunPreviousABTest(param: LandingPagesApiRerunPreviousABTestRequest, options?: ConfigurationOptions): Promise<void>;
    resetDraftWithHttpInfo(param: LandingPagesApiResetDraftRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    resetDraft(param: LandingPagesApiResetDraftRequest, options?: ConfigurationOptions): Promise<void>;
    restoreFolderPreviousVersionWithHttpInfo(param: LandingPagesApiRestoreFolderPreviousVersionRequest, options?: ConfigurationOptions): Promise<HttpInfo<ContentFolder>>;
    restoreFolderPreviousVersion(param: LandingPagesApiRestoreFolderPreviousVersionRequest, options?: ConfigurationOptions): Promise<ContentFolder>;
    restorePreviousVersionWithHttpInfo(param: LandingPagesApiRestorePreviousVersionRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    restorePreviousVersion(param: LandingPagesApiRestorePreviousVersionRequest, options?: ConfigurationOptions): Promise<Page>;
    restorePreviousVersionToDraftWithHttpInfo(param: LandingPagesApiRestorePreviousVersionToDraftRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    restorePreviousVersionToDraft(param: LandingPagesApiRestorePreviousVersionToDraftRequest, options?: ConfigurationOptions): Promise<Page>;
    scheduleWithHttpInfo(param: LandingPagesApiScheduleRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    schedule(param: LandingPagesApiScheduleRequest, options?: ConfigurationOptions): Promise<void>;
    setLangPrimaryWithHttpInfo(param: LandingPagesApiSetLangPrimaryRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    setLangPrimary(param: LandingPagesApiSetLangPrimaryRequest, options?: ConfigurationOptions): Promise<void>;
    updateWithHttpInfo(param: LandingPagesApiUpdateRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    update(param: LandingPagesApiUpdateRequest, options?: ConfigurationOptions): Promise<Page>;
    updateBatchWithHttpInfo(param: LandingPagesApiUpdateBatchRequest, options?: ConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    updateBatch(param: LandingPagesApiUpdateBatchRequest, options?: ConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    updateDraftWithHttpInfo(param: LandingPagesApiUpdateDraftRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    updateDraft(param: LandingPagesApiUpdateDraftRequest, options?: ConfigurationOptions): Promise<Page>;
    updateFolderWithHttpInfo(param: LandingPagesApiUpdateFolderRequest, options?: ConfigurationOptions): Promise<HttpInfo<ContentFolder>>;
    updateFolder(param: LandingPagesApiUpdateFolderRequest, options?: ConfigurationOptions): Promise<ContentFolder>;
    updateFoldersWithHttpInfo(param: LandingPagesApiUpdateFoldersRequest, options?: ConfigurationOptions): Promise<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    updateFolders(param: LandingPagesApiUpdateFoldersRequest, options?: ConfigurationOptions): Promise<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>;
    updateLangsWithHttpInfo(param: LandingPagesApiUpdateLangsRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    updateLangs(param: LandingPagesApiUpdateLangsRequest, options?: ConfigurationOptions): Promise<void>;
}
import { SitePagesApiRequestFactory, SitePagesApiResponseProcessor } from "../apis/SitePagesApi";
export interface SitePagesApiArchiveRequest {
    objectId: string;
    archived?: boolean;
}
export interface SitePagesApiArchiveBatchRequest {
    batchInputString: BatchInputString;
}
export interface SitePagesApiAttachToLangGroupRequest {
    attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext;
}
export interface SitePagesApiCloneRequest {
    contentCloneRequestVNext: ContentCloneRequestVNext;
}
export interface SitePagesApiCreateRequest {
    page: Page;
}
export interface SitePagesApiCreateABTestVariationRequest {
    abTestCreateRequestVNext: AbTestCreateRequestVNext;
}
export interface SitePagesApiCreateBatchRequest {
    batchInputPage: BatchInputPage;
}
export interface SitePagesApiCreateLangVariationRequest {
    contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext;
}
export interface SitePagesApiDetachFromLangGroupRequest {
    detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext;
}
export interface SitePagesApiEndActiveABTestRequest {
    abTestEndRequestVNext: AbTestEndRequestVNext;
}
export interface SitePagesApiGetByIdRequest {
    objectId: string;
    archived?: boolean;
    property?: string;
}
export interface SitePagesApiGetDraftByIdRequest {
    objectId: string;
}
export interface SitePagesApiGetPageRequest {
    createdAt?: Date;
    createdAfter?: Date;
    createdBefore?: Date;
    updatedAt?: Date;
    updatedAfter?: Date;
    updatedBefore?: Date;
    sort?: Array<string>;
    after?: string;
    limit?: number;
    archived?: boolean;
    property?: string;
}
export interface SitePagesApiGetPreviousVersionRequest {
    objectId: string;
    revisionId: string;
}
export interface SitePagesApiGetPreviousVersionsRequest {
    objectId: string;
    after?: string;
    before?: string;
    limit?: number;
}
export interface SitePagesApiPushLiveRequest {
    objectId: string;
}
export interface SitePagesApiReadBatchRequest {
    batchInputString: BatchInputString;
    archived?: boolean;
}
export interface SitePagesApiRerunPreviousABTestRequest {
    abTestRerunRequestVNext: AbTestRerunRequestVNext;
}
export interface SitePagesApiResetDraftRequest {
    objectId: string;
}
export interface SitePagesApiRestorePreviousVersionRequest {
    objectId: string;
    revisionId: string;
}
export interface SitePagesApiRestorePreviousVersionToDraftRequest {
    objectId: string;
    revisionId: number;
}
export interface SitePagesApiScheduleRequest {
    contentScheduleRequestVNext: ContentScheduleRequestVNext;
}
export interface SitePagesApiSetLangPrimaryRequest {
    setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext;
}
export interface SitePagesApiUpdateRequest {
    objectId: string;
    page: Page;
    archived?: boolean;
}
export interface SitePagesApiUpdateBatchRequest {
    batchInputJsonNode: BatchInputJsonNode;
    archived?: boolean;
}
export interface SitePagesApiUpdateDraftRequest {
    objectId: string;
    page: Page;
}
export interface SitePagesApiUpdateLangsRequest {
    updateLanguagesRequestVNext: UpdateLanguagesRequestVNext;
}
export declare class ObjectSitePagesApi {
    private api;
    constructor(configuration: Configuration, requestFactory?: SitePagesApiRequestFactory, responseProcessor?: SitePagesApiResponseProcessor);
    archiveWithHttpInfo(param: SitePagesApiArchiveRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    archive(param: SitePagesApiArchiveRequest, options?: ConfigurationOptions): Promise<void>;
    archiveBatchWithHttpInfo(param: SitePagesApiArchiveBatchRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    archiveBatch(param: SitePagesApiArchiveBatchRequest, options?: ConfigurationOptions): Promise<void>;
    attachToLangGroupWithHttpInfo(param: SitePagesApiAttachToLangGroupRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    attachToLangGroup(param: SitePagesApiAttachToLangGroupRequest, options?: ConfigurationOptions): Promise<void>;
    cloneWithHttpInfo(param: SitePagesApiCloneRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    clone(param: SitePagesApiCloneRequest, options?: ConfigurationOptions): Promise<Page>;
    createWithHttpInfo(param: SitePagesApiCreateRequest, options?: ConfigurationOptions): Promise<HttpInfo<void | Page>>;
    create(param: SitePagesApiCreateRequest, options?: ConfigurationOptions): Promise<void | Page>;
    createABTestVariationWithHttpInfo(param: SitePagesApiCreateABTestVariationRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    createABTestVariation(param: SitePagesApiCreateABTestVariationRequest, options?: ConfigurationOptions): Promise<Page>;
    createBatchWithHttpInfo(param: SitePagesApiCreateBatchRequest, options?: ConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    createBatch(param: SitePagesApiCreateBatchRequest, options?: ConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    createLangVariationWithHttpInfo(param: SitePagesApiCreateLangVariationRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    createLangVariation(param: SitePagesApiCreateLangVariationRequest, options?: ConfigurationOptions): Promise<Page>;
    detachFromLangGroupWithHttpInfo(param: SitePagesApiDetachFromLangGroupRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    detachFromLangGroup(param: SitePagesApiDetachFromLangGroupRequest, options?: ConfigurationOptions): Promise<void>;
    endActiveABTestWithHttpInfo(param: SitePagesApiEndActiveABTestRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    endActiveABTest(param: SitePagesApiEndActiveABTestRequest, options?: ConfigurationOptions): Promise<void>;
    getByIdWithHttpInfo(param: SitePagesApiGetByIdRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    getById(param: SitePagesApiGetByIdRequest, options?: ConfigurationOptions): Promise<Page>;
    getDraftByIdWithHttpInfo(param: SitePagesApiGetDraftByIdRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    getDraftById(param: SitePagesApiGetDraftByIdRequest, options?: ConfigurationOptions): Promise<Page>;
    getPageWithHttpInfo(param?: SitePagesApiGetPageRequest, options?: ConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalPageForwardPaging>>;
    getPage(param?: SitePagesApiGetPageRequest, options?: ConfigurationOptions): Promise<CollectionResponseWithTotalPageForwardPaging>;
    getPreviousVersionWithHttpInfo(param: SitePagesApiGetPreviousVersionRequest, options?: ConfigurationOptions): Promise<HttpInfo<VersionPage>>;
    getPreviousVersion(param: SitePagesApiGetPreviousVersionRequest, options?: ConfigurationOptions): Promise<VersionPage>;
    getPreviousVersionsWithHttpInfo(param: SitePagesApiGetPreviousVersionsRequest, options?: ConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalVersionPage>>;
    getPreviousVersions(param: SitePagesApiGetPreviousVersionsRequest, options?: ConfigurationOptions): Promise<CollectionResponseWithTotalVersionPage>;
    pushLiveWithHttpInfo(param: SitePagesApiPushLiveRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    pushLive(param: SitePagesApiPushLiveRequest, options?: ConfigurationOptions): Promise<void>;
    readBatchWithHttpInfo(param: SitePagesApiReadBatchRequest, options?: ConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    readBatch(param: SitePagesApiReadBatchRequest, options?: ConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    rerunPreviousABTestWithHttpInfo(param: SitePagesApiRerunPreviousABTestRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    rerunPreviousABTest(param: SitePagesApiRerunPreviousABTestRequest, options?: ConfigurationOptions): Promise<void>;
    resetDraftWithHttpInfo(param: SitePagesApiResetDraftRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    resetDraft(param: SitePagesApiResetDraftRequest, options?: ConfigurationOptions): Promise<void>;
    restorePreviousVersionWithHttpInfo(param: SitePagesApiRestorePreviousVersionRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    restorePreviousVersion(param: SitePagesApiRestorePreviousVersionRequest, options?: ConfigurationOptions): Promise<Page>;
    restorePreviousVersionToDraftWithHttpInfo(param: SitePagesApiRestorePreviousVersionToDraftRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    restorePreviousVersionToDraft(param: SitePagesApiRestorePreviousVersionToDraftRequest, options?: ConfigurationOptions): Promise<Page>;
    scheduleWithHttpInfo(param: SitePagesApiScheduleRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    schedule(param: SitePagesApiScheduleRequest, options?: ConfigurationOptions): Promise<void>;
    setLangPrimaryWithHttpInfo(param: SitePagesApiSetLangPrimaryRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    setLangPrimary(param: SitePagesApiSetLangPrimaryRequest, options?: ConfigurationOptions): Promise<void>;
    updateWithHttpInfo(param: SitePagesApiUpdateRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    update(param: SitePagesApiUpdateRequest, options?: ConfigurationOptions): Promise<Page>;
    updateBatchWithHttpInfo(param: SitePagesApiUpdateBatchRequest, options?: ConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    updateBatch(param: SitePagesApiUpdateBatchRequest, options?: ConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    updateDraftWithHttpInfo(param: SitePagesApiUpdateDraftRequest, options?: ConfigurationOptions): Promise<HttpInfo<Page>>;
    updateDraft(param: SitePagesApiUpdateDraftRequest, options?: ConfigurationOptions): Promise<Page>;
    updateLangsWithHttpInfo(param: SitePagesApiUpdateLangsRequest, options?: ConfigurationOptions): Promise<HttpInfo<void>>;
    updateLangs(param: SitePagesApiUpdateLangsRequest, options?: ConfigurationOptions): Promise<void>;
}
