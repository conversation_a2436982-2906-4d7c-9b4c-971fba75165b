{"version": 3, "file": "ObservableAPI.js", "sourceRoot": "", "sources": ["../../../../../codegen/cms/site_search/types/ObservableAPI.ts"], "names": [], "mappings": ";;;AAGA,0CAAmD;AACnD,0CAA2C;AAI3C,iDAAuF;AACvF,MAAa,mBAAmB;IAK5B,YACI,aAA4B,EAC5B,cAAwC,EACxC,iBAA8C;QAE9C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,IAAI,mCAAuB,CAAC,aAAa,CAAC,CAAC;QACnF,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,IAAI,IAAI,sCAA0B,EAAE,CAAC;IACnF,CAAC;IAQM,mBAAmB,CAAC,SAAiB,EAAE,IAAwF,EAAE,QAA+B;QACvK,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,aAAa,GAAiB,EAAE,CAAC;QACrC,IAAI,QAAQ,IAAI,QAAQ,CAAC,UAAU,EAAC;YAClC,MAAM,uBAAuB,GAAG,QAAQ,CAAC,uBAAuB,IAAI,SAAS,CAAA;YAE7E,MAAM,kBAAkB,GAAiB,QAAQ,CAAC,UAAU,CAAC;YAE7D,QAAO,uBAAuB,EAAC;gBAC/B,KAAK,QAAQ;oBACX,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;oBACzE,MAAM;gBACR,KAAK,SAAS;oBACZ,aAAa,GAAG,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;oBACxE,MAAM;gBACR,KAAK,SAAS;oBACZ,aAAa,GAAG,kBAAkB,CAAA;oBAClC,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,2CAA2C,uBAAuB,GAAG,CAAC,CAAA;aACvF;SACL;QACD,IAAI,QAAQ,EAAC;YACV,OAAO,GAAG;gBACR,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU;gBAChE,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO;gBACvD,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW;gBACnE,UAAU,EAAE,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU;aAC7D,CAAC;SACF;QAEM,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEpF,IAAI,uBAAuB,GAAG,IAAA,eAAI,EAAiB,qBAAqB,CAAC,CAAC;QAC1E,KAAK,MAAM,UAAU,IAAI,aAAa,EAAE;YACpC,uBAAuB,GAAG,uBAAuB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAClH;QAED,OAAO,uBAAuB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACxG,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,QAAyB,EAAE,EAAE;YACxC,IAAI,wBAAwB,GAAG,IAAA,aAAE,EAAC,QAAQ,CAAC,CAAC;YAC5C,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,OAAO,EAAE,EAAE;gBAC9C,wBAAwB,GAAG,wBAAwB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAoB,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACtH;YACD,OAAO,wBAAwB,CAAC,IAAI,CAAC,IAAA,cAAG,EAAC,CAAC,GAAoB,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACzH,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;IAQM,OAAO,CAAC,SAAiB,EAAE,IAAwF,EAAE,QAA+B;QACvJ,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAA,cAAG,EAAC,CAAC,WAAkC,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACnI,CAAC;IAuBM,kBAAkB,CAAC,CAAU,EAAE,KAAc,EAAE,MAAe,EAAE,QAAy6N,EAAE,WAAqB,EAAE,YAAsB,EAAE,eAAwB,EAAE,UAAmB,EAAE,WAAoB,EAAE,OAAgB,EAAE,UAAmB,EAAE,MAAsB,EAAE,IAA+F,EAAE,UAA0B,EAAE,QAAwB,EAAE,MAAyB,EAAE,OAAuB,EAAE,QAA+B;QAC/4O,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,aAAa,GAAiB,EAAE,CAAC;QACrC,IAAI,QAAQ,IAAI,QAAQ,CAAC,UAAU,EAAC;YAClC,MAAM,uBAAuB,GAAG,QAAQ,CAAC,uBAAuB,IAAI,SAAS,CAAA;YAE7E,MAAM,kBAAkB,GAAiB,QAAQ,CAAC,UAAU,CAAC;YAE7D,QAAO,uBAAuB,EAAC;gBAC/B,KAAK,QAAQ;oBACX,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;oBACzE,MAAM;gBACR,KAAK,SAAS;oBACZ,aAAa,GAAG,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;oBACxE,MAAM;gBACR,KAAK,SAAS;oBACZ,aAAa,GAAG,kBAAkB,CAAA;oBAClC,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,2CAA2C,uBAAuB,GAAG,CAAC,CAAA;aACvF;SACL;QACD,IAAI,QAAQ,EAAC;YACV,OAAO,GAAG;gBACR,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU;gBAChE,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO;gBACvD,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW;gBACnE,UAAU,EAAE,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU;aAC7D,CAAC;SACF;QAEM,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAE7O,IAAI,uBAAuB,GAAG,IAAA,eAAI,EAAiB,qBAAqB,CAAC,CAAC;QAC1E,KAAK,MAAM,UAAU,IAAI,aAAa,EAAE;YACpC,uBAAuB,GAAG,uBAAuB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAClH;QAED,OAAO,uBAAuB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACxG,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,QAAyB,EAAE,EAAE;YACxC,IAAI,wBAAwB,GAAG,IAAA,aAAE,EAAC,QAAQ,CAAC,CAAC;YAC5C,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,OAAO,EAAE,EAAE;gBAC9C,wBAAwB,GAAG,wBAAwB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAoB,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACtH;YACD,OAAO,wBAAwB,CAAC,IAAI,CAAC,IAAA,cAAG,EAAC,CAAC,GAAoB,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACxH,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;IAuBM,MAAM,CAAC,CAAU,EAAE,KAAc,EAAE,MAAe,EAAE,QAAy6N,EAAE,WAAqB,EAAE,YAAsB,EAAE,eAAwB,EAAE,UAAmB,EAAE,WAAoB,EAAE,OAAgB,EAAE,UAAmB,EAAE,MAAsB,EAAE,IAA+F,EAAE,UAA0B,EAAE,QAAwB,EAAE,MAAyB,EAAE,OAAuB,EAAE,QAA+B;QAC/3O,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAA,cAAG,EAAC,CAAC,WAA0C,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACpS,CAAC;CAEJ;AA7KD,kDA6KC"}