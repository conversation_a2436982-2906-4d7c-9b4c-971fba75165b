"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchResponseContentFolderWithErrorsStatusEnum = exports.BatchResponseContentFolderWithErrors = void 0;
class BatchResponseContentFolderWithErrors {
    static getAttributeTypeMap() {
        return BatchResponseContentFolderWithErrors.attributeTypeMap;
    }
    constructor() {
    }
}
exports.BatchResponseContentFolderWithErrors = BatchResponseContentFolderWithErrors;
BatchResponseContentFolderWithErrors.discriminator = undefined;
BatchResponseContentFolderWithErrors.mapping = undefined;
BatchResponseContentFolderWithErrors.attributeTypeMap = [
    {
        "name": "completedAt",
        "baseName": "completedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "numErrors",
        "baseName": "numErrors",
        "type": "number",
        "format": "int32"
    },
    {
        "name": "requestedAt",
        "baseName": "requestedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "startedAt",
        "baseName": "startedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "links",
        "baseName": "links",
        "type": "{ [key: string]: string; }",
        "format": ""
    },
    {
        "name": "results",
        "baseName": "results",
        "type": "Array<ContentFolder>",
        "format": ""
    },
    {
        "name": "errors",
        "baseName": "errors",
        "type": "Array<StandardError>",
        "format": ""
    },
    {
        "name": "status",
        "baseName": "status",
        "type": "BatchResponseContentFolderWithErrorsStatusEnum",
        "format": ""
    }
];
var BatchResponseContentFolderWithErrorsStatusEnum;
(function (BatchResponseContentFolderWithErrorsStatusEnum) {
    BatchResponseContentFolderWithErrorsStatusEnum["Pending"] = "PENDING";
    BatchResponseContentFolderWithErrorsStatusEnum["Processing"] = "PROCESSING";
    BatchResponseContentFolderWithErrorsStatusEnum["Canceled"] = "CANCELED";
    BatchResponseContentFolderWithErrorsStatusEnum["Complete"] = "COMPLETE";
})(BatchResponseContentFolderWithErrorsStatusEnum = exports.BatchResponseContentFolderWithErrorsStatusEnum || (exports.BatchResponseContentFolderWithErrorsStatusEnum = {}));
//# sourceMappingURL=BatchResponseContentFolderWithErrors.js.map