"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObservableValidationApi = exports.ObservableMetadataApi = exports.ObservableExtractApi = exports.ObservableContentApi = void 0;
const rxjsStub_1 = require("../rxjsStub");
const rxjsStub_2 = require("../rxjsStub");
const ContentApi_1 = require("../apis/ContentApi");
class ObservableContentApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.configuration = configuration;
        this.requestFactory = requestFactory || new ContentApi_1.ContentApiRequestFactory(configuration);
        this.responseProcessor = responseProcessor || new ContentApi_1.ContentApiResponseProcessor();
    }
    archiveWithHttpInfo(environment, path, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.archive(environment, path, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.archiveWithHttpInfo(rsp)));
        }));
    }
    archive(environment, path, _options) {
        return this.archiveWithHttpInfo(environment, path, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createWithHttpInfo(environment, path, file, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.create(environment, path, file, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createWithHttpInfo(rsp)));
        }));
    }
    create(environment, path, file, _options) {
        return this.createWithHttpInfo(environment, path, file, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    createOrUpdateWithHttpInfo(environment, path, file, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.createOrUpdate(environment, path, file, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.createOrUpdateWithHttpInfo(rsp)));
        }));
    }
    createOrUpdate(environment, path, file, _options) {
        return this.createOrUpdateWithHttpInfo(environment, path, file, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    downloadWithHttpInfo(environment, path, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.download(environment, path, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.downloadWithHttpInfo(rsp)));
        }));
    }
    download(environment, path, _options) {
        return this.downloadWithHttpInfo(environment, path, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
}
exports.ObservableContentApi = ObservableContentApi;
const ExtractApi_1 = require("../apis/ExtractApi");
class ObservableExtractApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.configuration = configuration;
        this.requestFactory = requestFactory || new ExtractApi_1.ExtractApiRequestFactory(configuration);
        this.responseProcessor = responseProcessor || new ExtractApi_1.ExtractApiResponseProcessor();
    }
    doAsyncWithHttpInfo(fileExtractRequest, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.doAsync(fileExtractRequest, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.doAsyncWithHttpInfo(rsp)));
        }));
    }
    doAsync(fileExtractRequest, _options) {
        return this.doAsyncWithHttpInfo(fileExtractRequest, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    getAsyncStatusWithHttpInfo(taskId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getAsyncStatus(taskId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getAsyncStatusWithHttpInfo(rsp)));
        }));
    }
    getAsyncStatus(taskId, _options) {
        return this.getAsyncStatusWithHttpInfo(taskId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
}
exports.ObservableExtractApi = ObservableExtractApi;
const MetadataApi_1 = require("../apis/MetadataApi");
class ObservableMetadataApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.configuration = configuration;
        this.requestFactory = requestFactory || new MetadataApi_1.MetadataApiRequestFactory(configuration);
        this.responseProcessor = responseProcessor || new MetadataApi_1.MetadataApiResponseProcessor();
    }
    getWithHttpInfo(environment, path, properties, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.get(environment, path, properties, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getWithHttpInfo(rsp)));
        }));
    }
    get(environment, path, properties, _options) {
        return this.getWithHttpInfo(environment, path, properties, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
}
exports.ObservableMetadataApi = ObservableMetadataApi;
const ValidationApi_1 = require("../apis/ValidationApi");
class ObservableValidationApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.configuration = configuration;
        this.requestFactory = requestFactory || new ValidationApi_1.ValidationApiRequestFactory(configuration);
        this.responseProcessor = responseProcessor || new ValidationApi_1.ValidationApiResponseProcessor();
    }
    doValidateWithHttpInfo(path, file, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.doValidate(path, file, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.doValidateWithHttpInfo(rsp)));
        }));
    }
    doValidate(path, file, _options) {
        return this.doValidateWithHttpInfo(path, file, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
}
exports.ObservableValidationApi = ObservableValidationApi;
//# sourceMappingURL=ObservableAPI.js.map