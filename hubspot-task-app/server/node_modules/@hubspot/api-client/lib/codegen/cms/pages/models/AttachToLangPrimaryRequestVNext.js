"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttachToLangPrimaryRequestVNext = void 0;
class AttachToLangPrimaryRequestVNext {
    static getAttributeTypeMap() {
        return AttachToLangPrimaryRequestVNext.attributeTypeMap;
    }
    constructor() {
    }
}
exports.AttachToLangPrimaryRequestVNext = AttachToLangPrimaryRequestVNext;
AttachToLangPrimaryRequestVNext.discriminator = undefined;
AttachToLangPrimaryRequestVNext.mapping = undefined;
AttachToLangPrimaryRequestVNext.attributeTypeMap = [
    {
        "name": "language",
        "baseName": "language",
        "type": "string",
        "format": ""
    },
    {
        "name": "id",
        "baseName": "id",
        "type": "string",
        "format": ""
    },
    {
        "name": "primaryId",
        "baseName": "primaryId",
        "type": "string",
        "format": ""
    },
    {
        "name": "primaryLanguage",
        "baseName": "primaryLanguage",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=AttachToLangPrimaryRequestVNext.js.map