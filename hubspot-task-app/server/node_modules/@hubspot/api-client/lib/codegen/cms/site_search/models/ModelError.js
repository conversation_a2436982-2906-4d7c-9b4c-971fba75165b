"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelError = void 0;
class ModelError {
    static getAttributeTypeMap() {
        return ModelError.attributeTypeMap;
    }
    constructor() {
    }
}
exports.ModelError = ModelError;
ModelError.discriminator = undefined;
ModelError.mapping = undefined;
ModelError.attributeTypeMap = [
    {
        "name": "subCategory",
        "baseName": "subCategory",
        "type": "string",
        "format": ""
    },
    {
        "name": "context",
        "baseName": "context",
        "type": "{ [key: string]: Array<string>; }",
        "format": ""
    },
    {
        "name": "correlationId",
        "baseName": "correlationId",
        "type": "string",
        "format": "uuid"
    },
    {
        "name": "links",
        "baseName": "links",
        "type": "{ [key: string]: string; }",
        "format": ""
    },
    {
        "name": "message",
        "baseName": "message",
        "type": "string",
        "format": ""
    },
    {
        "name": "category",
        "baseName": "category",
        "type": "string",
        "format": ""
    },
    {
        "name": "errors",
        "baseName": "errors",
        "type": "Array<ErrorDetail>",
        "format": ""
    }
];
//# sourceMappingURL=ModelError.js.map