{"version": 3, "file": "PromiseAPI.js", "sourceRoot": "", "sources": ["../../../../../codegen/cms/performance/types/PromiseAPI.ts"], "names": [], "mappings": ";;;AAIA,mDAAiE;AAGjE,MAAa,2BAA2B;IAGpC,YACI,aAA4B,EAC5B,cAAmD,EACnD,iBAAyD;QAEzD,IAAI,CAAC,GAAG,GAAG,IAAI,8CAA8B,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IACpG,CAAC;IAcM,mBAAmB,CAAC,MAAe,EAAE,IAAa,EAAE,GAAa,EAAE,GAAa,EAAE,MAAe,EAAE,QAAiB,EAAE,KAAc,EAAE,GAAY,EAAE,QAAwB;QAC/K,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QAC5G,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAcM,OAAO,CAAC,MAAe,EAAE,IAAa,EAAE,GAAa,EAAE,GAAa,EAAE,MAAe,EAAE,QAAiB,EAAE,KAAc,EAAE,GAAY,EAAE,QAAwB;QACnK,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QAChG,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAcM,qBAAqB,CAAC,MAAe,EAAE,IAAa,EAAE,GAAa,EAAE,GAAa,EAAE,MAAe,EAAE,QAAiB,EAAE,KAAc,EAAE,GAAY,EAAE,QAAwB;QACjL,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QAC9G,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAcM,SAAS,CAAC,MAAe,EAAE,IAAa,EAAE,GAAa,EAAE,GAAa,EAAE,MAAe,EAAE,QAAiB,EAAE,KAAc,EAAE,GAAY,EAAE,QAAwB;QACrK,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QAClG,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;CAGJ;AAhFD,kEAgFC"}