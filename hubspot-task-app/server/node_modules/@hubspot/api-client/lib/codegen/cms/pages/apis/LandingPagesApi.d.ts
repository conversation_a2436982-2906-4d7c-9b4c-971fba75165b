import { BaseAPIRequestFactory } from './baseapi';
import { Configuration } from '../configuration';
import { RequestContext, ResponseContext, HttpInfo } from '../http/http';
import { AbTestCreateRequestVNext } from '../models/AbTestCreateRequestVNext';
import { AbTestEndRequestVNext } from '../models/AbTestEndRequestVNext';
import { AbTestRerunRequestVNext } from '../models/AbTestRerunRequestVNext';
import { AttachToLangPrimaryRequestVNext } from '../models/AttachToLangPrimaryRequestVNext';
import { BatchInputContentFolder } from '../models/BatchInputContentFolder';
import { BatchInputJsonNode } from '../models/BatchInputJsonNode';
import { BatchInputPage } from '../models/BatchInputPage';
import { BatchInputString } from '../models/BatchInputString';
import { BatchResponseContentFolder } from '../models/BatchResponseContentFolder';
import { BatchResponseContentFolderWithErrors } from '../models/BatchResponseContentFolderWithErrors';
import { BatchResponsePage } from '../models/BatchResponsePage';
import { BatchResponsePageWithErrors } from '../models/BatchResponsePageWithErrors';
import { CollectionResponseWithTotalContentFolderForwardPaging } from '../models/CollectionResponseWithTotalContentFolderForwardPaging';
import { CollectionResponseWithTotalPageForwardPaging } from '../models/CollectionResponseWithTotalPageForwardPaging';
import { CollectionResponseWithTotalVersionContentFolder } from '../models/CollectionResponseWithTotalVersionContentFolder';
import { CollectionResponseWithTotalVersionPage } from '../models/CollectionResponseWithTotalVersionPage';
import { ContentCloneRequestVNext } from '../models/ContentCloneRequestVNext';
import { ContentFolder } from '../models/ContentFolder';
import { ContentLanguageCloneRequestVNext } from '../models/ContentLanguageCloneRequestVNext';
import { ContentScheduleRequestVNext } from '../models/ContentScheduleRequestVNext';
import { DetachFromLangGroupRequestVNext } from '../models/DetachFromLangGroupRequestVNext';
import { Page } from '../models/Page';
import { SetNewLanguagePrimaryRequestVNext } from '../models/SetNewLanguagePrimaryRequestVNext';
import { UpdateLanguagesRequestVNext } from '../models/UpdateLanguagesRequestVNext';
import { VersionContentFolder } from '../models/VersionContentFolder';
import { VersionPage } from '../models/VersionPage';
export declare class LandingPagesApiRequestFactory extends BaseAPIRequestFactory {
    archive(objectId: string, archived?: boolean, _options?: Configuration): Promise<RequestContext>;
    archiveBatch(batchInputString: BatchInputString, _options?: Configuration): Promise<RequestContext>;
    archiveFolder(objectId: string, archived?: boolean, _options?: Configuration): Promise<RequestContext>;
    archiveFolders(batchInputString: BatchInputString, _options?: Configuration): Promise<RequestContext>;
    attachToLangGroup(attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext, _options?: Configuration): Promise<RequestContext>;
    clone(contentCloneRequestVNext: ContentCloneRequestVNext, _options?: Configuration): Promise<RequestContext>;
    create(page: Page, _options?: Configuration): Promise<RequestContext>;
    createABTestVariation(abTestCreateRequestVNext: AbTestCreateRequestVNext, _options?: Configuration): Promise<RequestContext>;
    createBatch(batchInputPage: BatchInputPage, _options?: Configuration): Promise<RequestContext>;
    createFolder(contentFolder: ContentFolder, _options?: Configuration): Promise<RequestContext>;
    createFolders(batchInputContentFolder: BatchInputContentFolder, _options?: Configuration): Promise<RequestContext>;
    createLangVariation(contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext, _options?: Configuration): Promise<RequestContext>;
    detachFromLangGroup(detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext, _options?: Configuration): Promise<RequestContext>;
    endActiveABTest(abTestEndRequestVNext: AbTestEndRequestVNext, _options?: Configuration): Promise<RequestContext>;
    getById(objectId: string, archived?: boolean, property?: string, _options?: Configuration): Promise<RequestContext>;
    getDraftById(objectId: string, _options?: Configuration): Promise<RequestContext>;
    getFolderById(objectId: string, archived?: boolean, property?: string, _options?: Configuration): Promise<RequestContext>;
    getFolderPreviousVersion(objectId: string, revisionId: string, _options?: Configuration): Promise<RequestContext>;
    getFolderPreviousVersions(objectId: string, after?: string, before?: string, limit?: number, _options?: Configuration): Promise<RequestContext>;
    getFoldersPage(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: Configuration): Promise<RequestContext>;
    getPage(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: Configuration): Promise<RequestContext>;
    getPreviousVersion(objectId: string, revisionId: string, _options?: Configuration): Promise<RequestContext>;
    getPreviousVersions(objectId: string, after?: string, before?: string, limit?: number, _options?: Configuration): Promise<RequestContext>;
    pushLive(objectId: string, _options?: Configuration): Promise<RequestContext>;
    readBatch(batchInputString: BatchInputString, archived?: boolean, _options?: Configuration): Promise<RequestContext>;
    readFolders(batchInputString: BatchInputString, archived?: boolean, _options?: Configuration): Promise<RequestContext>;
    rerunPreviousABTest(abTestRerunRequestVNext: AbTestRerunRequestVNext, _options?: Configuration): Promise<RequestContext>;
    resetDraft(objectId: string, _options?: Configuration): Promise<RequestContext>;
    restoreFolderPreviousVersion(objectId: string, revisionId: string, _options?: Configuration): Promise<RequestContext>;
    restorePreviousVersion(objectId: string, revisionId: string, _options?: Configuration): Promise<RequestContext>;
    restorePreviousVersionToDraft(objectId: string, revisionId: number, _options?: Configuration): Promise<RequestContext>;
    schedule(contentScheduleRequestVNext: ContentScheduleRequestVNext, _options?: Configuration): Promise<RequestContext>;
    setLangPrimary(setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext, _options?: Configuration): Promise<RequestContext>;
    update(objectId: string, page: Page, archived?: boolean, _options?: Configuration): Promise<RequestContext>;
    updateBatch(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: Configuration): Promise<RequestContext>;
    updateDraft(objectId: string, page: Page, _options?: Configuration): Promise<RequestContext>;
    updateFolder(objectId: string, contentFolder: ContentFolder, archived?: boolean, _options?: Configuration): Promise<RequestContext>;
    updateFolders(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: Configuration): Promise<RequestContext>;
    updateLangs(updateLanguagesRequestVNext: UpdateLanguagesRequestVNext, _options?: Configuration): Promise<RequestContext>;
}
export declare class LandingPagesApiResponseProcessor {
    archiveWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    archiveBatchWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    archiveFolderWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    archiveFoldersWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    attachToLangGroupWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    cloneWithHttpInfo(response: ResponseContext): Promise<HttpInfo<Page>>;
    createWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void | Page>>;
    createABTestVariationWithHttpInfo(response: ResponseContext): Promise<HttpInfo<Page>>;
    createBatchWithHttpInfo(response: ResponseContext): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    createFolderWithHttpInfo(response: ResponseContext): Promise<HttpInfo<ContentFolder>>;
    createFoldersWithHttpInfo(response: ResponseContext): Promise<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    createLangVariationWithHttpInfo(response: ResponseContext): Promise<HttpInfo<Page>>;
    detachFromLangGroupWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    endActiveABTestWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    getByIdWithHttpInfo(response: ResponseContext): Promise<HttpInfo<Page>>;
    getDraftByIdWithHttpInfo(response: ResponseContext): Promise<HttpInfo<Page>>;
    getFolderByIdWithHttpInfo(response: ResponseContext): Promise<HttpInfo<ContentFolder>>;
    getFolderPreviousVersionWithHttpInfo(response: ResponseContext): Promise<HttpInfo<VersionContentFolder>>;
    getFolderPreviousVersionsWithHttpInfo(response: ResponseContext): Promise<HttpInfo<CollectionResponseWithTotalVersionContentFolder>>;
    getFoldersPageWithHttpInfo(response: ResponseContext): Promise<HttpInfo<CollectionResponseWithTotalContentFolderForwardPaging>>;
    getPageWithHttpInfo(response: ResponseContext): Promise<HttpInfo<CollectionResponseWithTotalPageForwardPaging>>;
    getPreviousVersionWithHttpInfo(response: ResponseContext): Promise<HttpInfo<VersionPage>>;
    getPreviousVersionsWithHttpInfo(response: ResponseContext): Promise<HttpInfo<CollectionResponseWithTotalVersionPage>>;
    pushLiveWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    readBatchWithHttpInfo(response: ResponseContext): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    readFoldersWithHttpInfo(response: ResponseContext): Promise<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    rerunPreviousABTestWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    resetDraftWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    restoreFolderPreviousVersionWithHttpInfo(response: ResponseContext): Promise<HttpInfo<ContentFolder>>;
    restorePreviousVersionWithHttpInfo(response: ResponseContext): Promise<HttpInfo<Page>>;
    restorePreviousVersionToDraftWithHttpInfo(response: ResponseContext): Promise<HttpInfo<Page>>;
    scheduleWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    setLangPrimaryWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
    updateWithHttpInfo(response: ResponseContext): Promise<HttpInfo<Page>>;
    updateBatchWithHttpInfo(response: ResponseContext): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    updateDraftWithHttpInfo(response: ResponseContext): Promise<HttpInfo<Page>>;
    updateFolderWithHttpInfo(response: ResponseContext): Promise<HttpInfo<ContentFolder>>;
    updateFoldersWithHttpInfo(response: ResponseContext): Promise<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    updateLangsWithHttpInfo(response: ResponseContext): Promise<HttpInfo<void>>;
}
