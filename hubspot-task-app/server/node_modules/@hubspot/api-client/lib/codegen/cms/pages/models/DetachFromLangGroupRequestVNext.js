"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DetachFromLangGroupRequestVNext = void 0;
class DetachFromLangGroupRequestVNext {
    static getAttributeTypeMap() {
        return DetachFromLangGroupRequestVNext.attributeTypeMap;
    }
    constructor() {
    }
}
exports.DetachFromLangGroupRequestVNext = DetachFromLangGroupRequestVNext;
DetachFromLangGroupRequestVNext.discriminator = undefined;
DetachFromLangGroupRequestVNext.mapping = undefined;
DetachFromLangGroupRequestVNext.attributeTypeMap = [
    {
        "name": "id",
        "baseName": "id",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=DetachFromLangGroupRequestVNext.js.map