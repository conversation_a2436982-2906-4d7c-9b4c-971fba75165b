"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromiseValidationApi = exports.PromiseMetadataApi = exports.PromiseExtractApi = exports.PromiseContentApi = void 0;
const middleware_1 = require("../middleware");
const ObservableAPI_1 = require("./ObservableAPI");
class PromiseContentApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.api = new ObservableAPI_1.ObservableContentApi(configuration, requestFactory, responseProcessor);
    }
    archiveWithHttpInfo(environment, path, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveWithHttpInfo(environment, path, observableOptions);
        return result.toPromise();
    }
    archive(environment, path, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archive(environment, path, observableOptions);
        return result.toPromise();
    }
    createWithHttpInfo(environment, path, file, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createWithHttpInfo(environment, path, file, observableOptions);
        return result.toPromise();
    }
    create(environment, path, file, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.create(environment, path, file, observableOptions);
        return result.toPromise();
    }
    createOrUpdateWithHttpInfo(environment, path, file, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createOrUpdateWithHttpInfo(environment, path, file, observableOptions);
        return result.toPromise();
    }
    createOrUpdate(environment, path, file, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createOrUpdate(environment, path, file, observableOptions);
        return result.toPromise();
    }
    downloadWithHttpInfo(environment, path, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.downloadWithHttpInfo(environment, path, observableOptions);
        return result.toPromise();
    }
    download(environment, path, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.download(environment, path, observableOptions);
        return result.toPromise();
    }
}
exports.PromiseContentApi = PromiseContentApi;
const ObservableAPI_2 = require("./ObservableAPI");
class PromiseExtractApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.api = new ObservableAPI_2.ObservableExtractApi(configuration, requestFactory, responseProcessor);
    }
    doAsyncWithHttpInfo(fileExtractRequest, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.doAsyncWithHttpInfo(fileExtractRequest, observableOptions);
        return result.toPromise();
    }
    doAsync(fileExtractRequest, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.doAsync(fileExtractRequest, observableOptions);
        return result.toPromise();
    }
    getAsyncStatusWithHttpInfo(taskId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getAsyncStatusWithHttpInfo(taskId, observableOptions);
        return result.toPromise();
    }
    getAsyncStatus(taskId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getAsyncStatus(taskId, observableOptions);
        return result.toPromise();
    }
}
exports.PromiseExtractApi = PromiseExtractApi;
const ObservableAPI_3 = require("./ObservableAPI");
class PromiseMetadataApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.api = new ObservableAPI_3.ObservableMetadataApi(configuration, requestFactory, responseProcessor);
    }
    getWithHttpInfo(environment, path, properties, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getWithHttpInfo(environment, path, properties, observableOptions);
        return result.toPromise();
    }
    get(environment, path, properties, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.get(environment, path, properties, observableOptions);
        return result.toPromise();
    }
}
exports.PromiseMetadataApi = PromiseMetadataApi;
const ObservableAPI_4 = require("./ObservableAPI");
class PromiseValidationApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.api = new ObservableAPI_4.ObservableValidationApi(configuration, requestFactory, responseProcessor);
    }
    doValidateWithHttpInfo(path, file, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.doValidateWithHttpInfo(path, file, observableOptions);
        return result.toPromise();
    }
    doValidate(path, file, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.doValidate(path, file, observableOptions);
        return result.toPromise();
    }
}
exports.PromiseValidationApi = PromiseValidationApi;
//# sourceMappingURL=PromiseAPI.js.map