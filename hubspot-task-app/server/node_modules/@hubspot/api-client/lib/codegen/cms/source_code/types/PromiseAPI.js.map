{"version": 3, "file": "PromiseAPI.js", "sourceRoot": "", "sources": ["../../../../../codegen/cms/source_code/types/PromiseAPI.ts"], "names": [], "mappings": ";;;AAEA,8CAAyD;AAMzD,mDAAuD;AAGvD,MAAa,iBAAiB;IAG1B,YACI,aAA4B,EAC5B,cAAyC,EACzC,iBAA+C;QAE/C,IAAI,CAAC,GAAG,GAAG,IAAI,oCAAoB,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC1F,CAAC;IAQM,mBAAmB,CAAC,WAAmB,EAAE,IAAY,EAAE,QAAsC;;QAChG,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAClF,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAQM,OAAO,CAAC,WAAmB,EAAE,IAAY,EAAE,QAAsC;;QACpF,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QACtE,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IASM,kBAAkB,CAAC,WAAmB,EAAE,IAAY,EAAE,IAAe,EAAE,QAAsC;;QAChH,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QACvF,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IASM,MAAM,CAAC,WAAmB,EAAE,IAAY,EAAE,IAAe,EAAE,QAAsC;;QACpG,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAC3E,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IASM,0BAA0B,CAAC,WAAmB,EAAE,IAAY,EAAE,IAAe,EAAE,QAAsC;;QACxH,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAC/F,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IASM,cAAc,CAAC,WAAmB,EAAE,IAAY,EAAE,IAAe,EAAE,QAAsC;;QAC5G,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QACnF,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAQM,oBAAoB,CAAC,WAAmB,EAAE,IAAY,EAAE,QAAsC;;QACjG,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QACnF,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAQM,QAAQ,CAAC,WAAmB,EAAE,IAAY,EAAE,QAAsC;;QACrF,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QACvE,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;CAGJ;AAxMD,8CAwMC;AAID,mDAAuD;AAGvD,MAAa,iBAAiB;IAG1B,YACI,aAA4B,EAC5B,cAAyC,EACzC,iBAA+C;QAE/C,IAAI,CAAC,GAAG,GAAG,IAAI,oCAAoB,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC1F,CAAC;IAOM,mBAAmB,CAAC,kBAAsC,EAAE,QAAsC;;QACrG,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;QACnF,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAOM,OAAO,CAAC,kBAAsC,EAAE,QAAsC;;QACzF,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;QACvE,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAOM,0BAA0B,CAAC,MAAc,EAAE,QAAsC;;QACpF,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAC9E,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAOM,cAAc,CAAC,MAAc,EAAE,QAAsC;;QACxE,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAClE,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;CAGJ;AApGD,8CAoGC;AAID,mDAAwD;AAGxD,MAAa,kBAAkB;IAG3B,YACI,aAA4B,EAC5B,cAA0C,EAC1C,iBAAgD;QAEhD,IAAI,CAAC,GAAG,GAAG,IAAI,qCAAqB,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC3F,CAAC;IASM,eAAe,CAAC,WAAmB,EAAE,IAAY,EAAE,UAAmB,EAAE,QAAsC;;QACjH,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;QAC1F,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IASM,GAAG,CAAC,WAAmB,EAAE,IAAY,EAAE,UAAmB,EAAE,QAAsC;;QACrG,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;QAC9E,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;CAGJ;AA5DD,gDA4DC;AAID,mDAA0D;AAG1D,MAAa,oBAAoB;IAG7B,YACI,aAA4B,EAC5B,cAA4C,EAC5C,iBAAkD;QAElD,IAAI,CAAC,GAAG,GAAG,IAAI,uCAAuB,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC7F,CAAC;IAQM,sBAAsB,CAAC,IAAY,EAAE,IAAe,EAAE,QAAsC;;QAC/F,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAC9E,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAQM,UAAU,CAAC,IAAY,EAAE,IAAe,EAAE,QAAsC;;QACnF,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAClE,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;CAGJ;AA1DD,oDA0DC"}