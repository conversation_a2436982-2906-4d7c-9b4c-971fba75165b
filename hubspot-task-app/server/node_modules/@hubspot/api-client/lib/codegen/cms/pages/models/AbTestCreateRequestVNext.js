"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AbTestCreateRequestVNext = void 0;
class AbTestCreateRequestVNext {
    static getAttributeTypeMap() {
        return AbTestCreateRequestVNext.attributeTypeMap;
    }
    constructor() {
    }
}
exports.AbTestCreateRequestVNext = AbTestCreateRequestVNext;
AbTestCreateRequestVNext.discriminator = undefined;
AbTestCreateRequestVNext.mapping = undefined;
AbTestCreateRequestVNext.attributeTypeMap = [
    {
        "name": "variationName",
        "baseName": "variationName",
        "type": "string",
        "format": ""
    },
    {
        "name": "contentId",
        "baseName": "contentId",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=AbTestCreateRequestVNext.js.map