"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromiseSitePagesApi = exports.PromiseLandingPagesApi = void 0;
const middleware_1 = require("../middleware");
const ObservableAPI_1 = require("./ObservableAPI");
class PromiseLandingPagesApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.api = new ObservableAPI_1.ObservableLandingPagesApi(configuration, requestFactory, responseProcessor);
    }
    archiveWithHttpInfo(objectId, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveWithHttpInfo(objectId, archived, observableOptions);
        return result.toPromise();
    }
    archive(objectId, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archive(objectId, archived, observableOptions);
        return result.toPromise();
    }
    archiveBatchWithHttpInfo(batchInputString, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveBatchWithHttpInfo(batchInputString, observableOptions);
        return result.toPromise();
    }
    archiveBatch(batchInputString, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveBatch(batchInputString, observableOptions);
        return result.toPromise();
    }
    archiveFolderWithHttpInfo(objectId, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveFolderWithHttpInfo(objectId, archived, observableOptions);
        return result.toPromise();
    }
    archiveFolder(objectId, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveFolder(objectId, archived, observableOptions);
        return result.toPromise();
    }
    archiveFoldersWithHttpInfo(batchInputString, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveFoldersWithHttpInfo(batchInputString, observableOptions);
        return result.toPromise();
    }
    archiveFolders(batchInputString, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveFolders(batchInputString, observableOptions);
        return result.toPromise();
    }
    attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext, observableOptions);
        return result.toPromise();
    }
    attachToLangGroup(attachToLangPrimaryRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.attachToLangGroup(attachToLangPrimaryRequestVNext, observableOptions);
        return result.toPromise();
    }
    cloneWithHttpInfo(contentCloneRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.cloneWithHttpInfo(contentCloneRequestVNext, observableOptions);
        return result.toPromise();
    }
    clone(contentCloneRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.clone(contentCloneRequestVNext, observableOptions);
        return result.toPromise();
    }
    createWithHttpInfo(page, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createWithHttpInfo(page, observableOptions);
        return result.toPromise();
    }
    create(page, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.create(page, observableOptions);
        return result.toPromise();
    }
    createABTestVariationWithHttpInfo(abTestCreateRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createABTestVariationWithHttpInfo(abTestCreateRequestVNext, observableOptions);
        return result.toPromise();
    }
    createABTestVariation(abTestCreateRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createABTestVariation(abTestCreateRequestVNext, observableOptions);
        return result.toPromise();
    }
    createBatchWithHttpInfo(batchInputPage, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createBatchWithHttpInfo(batchInputPage, observableOptions);
        return result.toPromise();
    }
    createBatch(batchInputPage, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createBatch(batchInputPage, observableOptions);
        return result.toPromise();
    }
    createFolderWithHttpInfo(contentFolder, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createFolderWithHttpInfo(contentFolder, observableOptions);
        return result.toPromise();
    }
    createFolder(contentFolder, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createFolder(contentFolder, observableOptions);
        return result.toPromise();
    }
    createFoldersWithHttpInfo(batchInputContentFolder, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createFoldersWithHttpInfo(batchInputContentFolder, observableOptions);
        return result.toPromise();
    }
    createFolders(batchInputContentFolder, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createFolders(batchInputContentFolder, observableOptions);
        return result.toPromise();
    }
    createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext, observableOptions);
        return result.toPromise();
    }
    createLangVariation(contentLanguageCloneRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createLangVariation(contentLanguageCloneRequestVNext, observableOptions);
        return result.toPromise();
    }
    detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext, observableOptions);
        return result.toPromise();
    }
    detachFromLangGroup(detachFromLangGroupRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.detachFromLangGroup(detachFromLangGroupRequestVNext, observableOptions);
        return result.toPromise();
    }
    endActiveABTestWithHttpInfo(abTestEndRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.endActiveABTestWithHttpInfo(abTestEndRequestVNext, observableOptions);
        return result.toPromise();
    }
    endActiveABTest(abTestEndRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.endActiveABTest(abTestEndRequestVNext, observableOptions);
        return result.toPromise();
    }
    getByIdWithHttpInfo(objectId, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getByIdWithHttpInfo(objectId, archived, property, observableOptions);
        return result.toPromise();
    }
    getById(objectId, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getById(objectId, archived, property, observableOptions);
        return result.toPromise();
    }
    getDraftByIdWithHttpInfo(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getDraftByIdWithHttpInfo(objectId, observableOptions);
        return result.toPromise();
    }
    getDraftById(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getDraftById(objectId, observableOptions);
        return result.toPromise();
    }
    getFolderByIdWithHttpInfo(objectId, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getFolderByIdWithHttpInfo(objectId, archived, property, observableOptions);
        return result.toPromise();
    }
    getFolderById(objectId, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getFolderById(objectId, archived, property, observableOptions);
        return result.toPromise();
    }
    getFolderPreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getFolderPreviousVersionWithHttpInfo(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    getFolderPreviousVersion(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getFolderPreviousVersion(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    getFolderPreviousVersionsWithHttpInfo(objectId, after, before, limit, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getFolderPreviousVersionsWithHttpInfo(objectId, after, before, limit, observableOptions);
        return result.toPromise();
    }
    getFolderPreviousVersions(objectId, after, before, limit, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getFolderPreviousVersions(objectId, after, before, limit, observableOptions);
        return result.toPromise();
    }
    getFoldersPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getFoldersPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, observableOptions);
        return result.toPromise();
    }
    getFoldersPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getFoldersPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, observableOptions);
        return result.toPromise();
    }
    getPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, observableOptions);
        return result.toPromise();
    }
    getPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, observableOptions);
        return result.toPromise();
    }
    getPreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPreviousVersionWithHttpInfo(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    getPreviousVersion(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPreviousVersion(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    getPreviousVersionsWithHttpInfo(objectId, after, before, limit, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPreviousVersionsWithHttpInfo(objectId, after, before, limit, observableOptions);
        return result.toPromise();
    }
    getPreviousVersions(objectId, after, before, limit, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPreviousVersions(objectId, after, before, limit, observableOptions);
        return result.toPromise();
    }
    pushLiveWithHttpInfo(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.pushLiveWithHttpInfo(objectId, observableOptions);
        return result.toPromise();
    }
    pushLive(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.pushLive(objectId, observableOptions);
        return result.toPromise();
    }
    readBatchWithHttpInfo(batchInputString, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.readBatchWithHttpInfo(batchInputString, archived, observableOptions);
        return result.toPromise();
    }
    readBatch(batchInputString, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.readBatch(batchInputString, archived, observableOptions);
        return result.toPromise();
    }
    readFoldersWithHttpInfo(batchInputString, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.readFoldersWithHttpInfo(batchInputString, archived, observableOptions);
        return result.toPromise();
    }
    readFolders(batchInputString, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.readFolders(batchInputString, archived, observableOptions);
        return result.toPromise();
    }
    rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext, observableOptions);
        return result.toPromise();
    }
    rerunPreviousABTest(abTestRerunRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.rerunPreviousABTest(abTestRerunRequestVNext, observableOptions);
        return result.toPromise();
    }
    resetDraftWithHttpInfo(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.resetDraftWithHttpInfo(objectId, observableOptions);
        return result.toPromise();
    }
    resetDraft(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.resetDraft(objectId, observableOptions);
        return result.toPromise();
    }
    restoreFolderPreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restoreFolderPreviousVersionWithHttpInfo(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    restoreFolderPreviousVersion(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restoreFolderPreviousVersion(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    restorePreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restorePreviousVersionWithHttpInfo(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    restorePreviousVersion(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restorePreviousVersion(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    restorePreviousVersionToDraftWithHttpInfo(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restorePreviousVersionToDraftWithHttpInfo(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    restorePreviousVersionToDraft(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restorePreviousVersionToDraft(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    scheduleWithHttpInfo(contentScheduleRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.scheduleWithHttpInfo(contentScheduleRequestVNext, observableOptions);
        return result.toPromise();
    }
    schedule(contentScheduleRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.schedule(contentScheduleRequestVNext, observableOptions);
        return result.toPromise();
    }
    setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext, observableOptions);
        return result.toPromise();
    }
    setLangPrimary(setNewLanguagePrimaryRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.setLangPrimary(setNewLanguagePrimaryRequestVNext, observableOptions);
        return result.toPromise();
    }
    updateWithHttpInfo(objectId, page, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateWithHttpInfo(objectId, page, archived, observableOptions);
        return result.toPromise();
    }
    update(objectId, page, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.update(objectId, page, archived, observableOptions);
        return result.toPromise();
    }
    updateBatchWithHttpInfo(batchInputJsonNode, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateBatchWithHttpInfo(batchInputJsonNode, archived, observableOptions);
        return result.toPromise();
    }
    updateBatch(batchInputJsonNode, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateBatch(batchInputJsonNode, archived, observableOptions);
        return result.toPromise();
    }
    updateDraftWithHttpInfo(objectId, page, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateDraftWithHttpInfo(objectId, page, observableOptions);
        return result.toPromise();
    }
    updateDraft(objectId, page, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateDraft(objectId, page, observableOptions);
        return result.toPromise();
    }
    updateFolderWithHttpInfo(objectId, contentFolder, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateFolderWithHttpInfo(objectId, contentFolder, archived, observableOptions);
        return result.toPromise();
    }
    updateFolder(objectId, contentFolder, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateFolder(objectId, contentFolder, archived, observableOptions);
        return result.toPromise();
    }
    updateFoldersWithHttpInfo(batchInputJsonNode, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateFoldersWithHttpInfo(batchInputJsonNode, archived, observableOptions);
        return result.toPromise();
    }
    updateFolders(batchInputJsonNode, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateFolders(batchInputJsonNode, archived, observableOptions);
        return result.toPromise();
    }
    updateLangsWithHttpInfo(updateLanguagesRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateLangsWithHttpInfo(updateLanguagesRequestVNext, observableOptions);
        return result.toPromise();
    }
    updateLangs(updateLanguagesRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateLangs(updateLanguagesRequestVNext, observableOptions);
        return result.toPromise();
    }
}
exports.PromiseLandingPagesApi = PromiseLandingPagesApi;
const ObservableAPI_2 = require("./ObservableAPI");
class PromiseSitePagesApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.api = new ObservableAPI_2.ObservableSitePagesApi(configuration, requestFactory, responseProcessor);
    }
    archiveWithHttpInfo(objectId, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveWithHttpInfo(objectId, archived, observableOptions);
        return result.toPromise();
    }
    archive(objectId, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archive(objectId, archived, observableOptions);
        return result.toPromise();
    }
    archiveBatchWithHttpInfo(batchInputString, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveBatchWithHttpInfo(batchInputString, observableOptions);
        return result.toPromise();
    }
    archiveBatch(batchInputString, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.archiveBatch(batchInputString, observableOptions);
        return result.toPromise();
    }
    attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext, observableOptions);
        return result.toPromise();
    }
    attachToLangGroup(attachToLangPrimaryRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.attachToLangGroup(attachToLangPrimaryRequestVNext, observableOptions);
        return result.toPromise();
    }
    cloneWithHttpInfo(contentCloneRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.cloneWithHttpInfo(contentCloneRequestVNext, observableOptions);
        return result.toPromise();
    }
    clone(contentCloneRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.clone(contentCloneRequestVNext, observableOptions);
        return result.toPromise();
    }
    createWithHttpInfo(page, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createWithHttpInfo(page, observableOptions);
        return result.toPromise();
    }
    create(page, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.create(page, observableOptions);
        return result.toPromise();
    }
    createABTestVariationWithHttpInfo(abTestCreateRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createABTestVariationWithHttpInfo(abTestCreateRequestVNext, observableOptions);
        return result.toPromise();
    }
    createABTestVariation(abTestCreateRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createABTestVariation(abTestCreateRequestVNext, observableOptions);
        return result.toPromise();
    }
    createBatchWithHttpInfo(batchInputPage, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createBatchWithHttpInfo(batchInputPage, observableOptions);
        return result.toPromise();
    }
    createBatch(batchInputPage, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createBatch(batchInputPage, observableOptions);
        return result.toPromise();
    }
    createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext, observableOptions);
        return result.toPromise();
    }
    createLangVariation(contentLanguageCloneRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.createLangVariation(contentLanguageCloneRequestVNext, observableOptions);
        return result.toPromise();
    }
    detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext, observableOptions);
        return result.toPromise();
    }
    detachFromLangGroup(detachFromLangGroupRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.detachFromLangGroup(detachFromLangGroupRequestVNext, observableOptions);
        return result.toPromise();
    }
    endActiveABTestWithHttpInfo(abTestEndRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.endActiveABTestWithHttpInfo(abTestEndRequestVNext, observableOptions);
        return result.toPromise();
    }
    endActiveABTest(abTestEndRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.endActiveABTest(abTestEndRequestVNext, observableOptions);
        return result.toPromise();
    }
    getByIdWithHttpInfo(objectId, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getByIdWithHttpInfo(objectId, archived, property, observableOptions);
        return result.toPromise();
    }
    getById(objectId, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getById(objectId, archived, property, observableOptions);
        return result.toPromise();
    }
    getDraftByIdWithHttpInfo(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getDraftByIdWithHttpInfo(objectId, observableOptions);
        return result.toPromise();
    }
    getDraftById(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getDraftById(objectId, observableOptions);
        return result.toPromise();
    }
    getPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPageWithHttpInfo(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, observableOptions);
        return result.toPromise();
    }
    getPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, observableOptions);
        return result.toPromise();
    }
    getPreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPreviousVersionWithHttpInfo(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    getPreviousVersion(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPreviousVersion(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    getPreviousVersionsWithHttpInfo(objectId, after, before, limit, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPreviousVersionsWithHttpInfo(objectId, after, before, limit, observableOptions);
        return result.toPromise();
    }
    getPreviousVersions(objectId, after, before, limit, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getPreviousVersions(objectId, after, before, limit, observableOptions);
        return result.toPromise();
    }
    pushLiveWithHttpInfo(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.pushLiveWithHttpInfo(objectId, observableOptions);
        return result.toPromise();
    }
    pushLive(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.pushLive(objectId, observableOptions);
        return result.toPromise();
    }
    readBatchWithHttpInfo(batchInputString, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.readBatchWithHttpInfo(batchInputString, archived, observableOptions);
        return result.toPromise();
    }
    readBatch(batchInputString, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.readBatch(batchInputString, archived, observableOptions);
        return result.toPromise();
    }
    rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext, observableOptions);
        return result.toPromise();
    }
    rerunPreviousABTest(abTestRerunRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.rerunPreviousABTest(abTestRerunRequestVNext, observableOptions);
        return result.toPromise();
    }
    resetDraftWithHttpInfo(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.resetDraftWithHttpInfo(objectId, observableOptions);
        return result.toPromise();
    }
    resetDraft(objectId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.resetDraft(objectId, observableOptions);
        return result.toPromise();
    }
    restorePreviousVersionWithHttpInfo(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restorePreviousVersionWithHttpInfo(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    restorePreviousVersion(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restorePreviousVersion(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    restorePreviousVersionToDraftWithHttpInfo(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restorePreviousVersionToDraftWithHttpInfo(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    restorePreviousVersionToDraft(objectId, revisionId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.restorePreviousVersionToDraft(objectId, revisionId, observableOptions);
        return result.toPromise();
    }
    scheduleWithHttpInfo(contentScheduleRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.scheduleWithHttpInfo(contentScheduleRequestVNext, observableOptions);
        return result.toPromise();
    }
    schedule(contentScheduleRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.schedule(contentScheduleRequestVNext, observableOptions);
        return result.toPromise();
    }
    setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext, observableOptions);
        return result.toPromise();
    }
    setLangPrimary(setNewLanguagePrimaryRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.setLangPrimary(setNewLanguagePrimaryRequestVNext, observableOptions);
        return result.toPromise();
    }
    updateWithHttpInfo(objectId, page, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateWithHttpInfo(objectId, page, archived, observableOptions);
        return result.toPromise();
    }
    update(objectId, page, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.update(objectId, page, archived, observableOptions);
        return result.toPromise();
    }
    updateBatchWithHttpInfo(batchInputJsonNode, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateBatchWithHttpInfo(batchInputJsonNode, archived, observableOptions);
        return result.toPromise();
    }
    updateBatch(batchInputJsonNode, archived, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateBatch(batchInputJsonNode, archived, observableOptions);
        return result.toPromise();
    }
    updateDraftWithHttpInfo(objectId, page, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateDraftWithHttpInfo(objectId, page, observableOptions);
        return result.toPromise();
    }
    updateDraft(objectId, page, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateDraft(objectId, page, observableOptions);
        return result.toPromise();
    }
    updateLangsWithHttpInfo(updateLanguagesRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateLangsWithHttpInfo(updateLanguagesRequestVNext, observableOptions);
        return result.toPromise();
    }
    updateLangs(updateLanguagesRequestVNext, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.updateLangs(updateLanguagesRequestVNext, observableOptions);
        return result.toPromise();
    }
}
exports.PromiseSitePagesApi = PromiseSitePagesApi;
//# sourceMappingURL=PromiseAPI.js.map