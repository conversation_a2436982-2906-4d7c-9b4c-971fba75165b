"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetFileMetadata = void 0;
class AssetFileMetadata {
    static getAttributeTypeMap() {
        return AssetFileMetadata.attributeTypeMap;
    }
    constructor() {
    }
}
exports.AssetFileMetadata = AssetFileMetadata;
AssetFileMetadata.discriminator = undefined;
AssetFileMetadata.mapping = undefined;
AssetFileMetadata.attributeTypeMap = [
    {
        "name": "createdAt",
        "baseName": "createdAt",
        "type": "number",
        "format": "int64"
    },
    {
        "name": "archivedAt",
        "baseName": "archivedAt",
        "type": "number",
        "format": "int64"
    },
    {
        "name": "folder",
        "baseName": "folder",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "children",
        "baseName": "children",
        "type": "Array<string>",
        "format": ""
    },
    {
        "name": "name",
        "baseName": "name",
        "type": "string",
        "format": ""
    },
    {
        "name": "id",
        "baseName": "id",
        "type": "string",
        "format": ""
    },
    {
        "name": "hash",
        "baseName": "hash",
        "type": "string",
        "format": ""
    },
    {
        "name": "updatedAt",
        "baseName": "updatedAt",
        "type": "number",
        "format": "int64"
    }
];
//# sourceMappingURL=AssetFileMetadata.js.map