import { HttpInfo } from '../http/http';
import { Configuration, PromiseConfigurationOptions } from '../configuration';
import { AbTestCreateRequestVNext } from '../models/AbTestCreateRequestVNext';
import { AbTestEndRequestVNext } from '../models/AbTestEndRequestVNext';
import { AbTestRerunRequestVNext } from '../models/AbTestRerunRequestVNext';
import { AttachToLangPrimaryRequestVNext } from '../models/AttachToLangPrimaryRequestVNext';
import { BatchInputContentFolder } from '../models/BatchInputContentFolder';
import { BatchInputJsonNode } from '../models/BatchInputJsonNode';
import { BatchInputPage } from '../models/BatchInputPage';
import { BatchInputString } from '../models/BatchInputString';
import { BatchResponseContentFolder } from '../models/BatchResponseContentFolder';
import { BatchResponseContentFolderWithErrors } from '../models/BatchResponseContentFolderWithErrors';
import { BatchResponsePage } from '../models/BatchResponsePage';
import { BatchResponsePageWithErrors } from '../models/BatchResponsePageWithErrors';
import { CollectionResponseWithTotalContentFolderForwardPaging } from '../models/CollectionResponseWithTotalContentFolderForwardPaging';
import { CollectionResponseWithTotalPageForwardPaging } from '../models/CollectionResponseWithTotalPageForwardPaging';
import { CollectionResponseWithTotalVersionContentFolder } from '../models/CollectionResponseWithTotalVersionContentFolder';
import { CollectionResponseWithTotalVersionPage } from '../models/CollectionResponseWithTotalVersionPage';
import { ContentCloneRequestVNext } from '../models/ContentCloneRequestVNext';
import { ContentFolder } from '../models/ContentFolder';
import { ContentLanguageCloneRequestVNext } from '../models/ContentLanguageCloneRequestVNext';
import { ContentScheduleRequestVNext } from '../models/ContentScheduleRequestVNext';
import { DetachFromLangGroupRequestVNext } from '../models/DetachFromLangGroupRequestVNext';
import { Page } from '../models/Page';
import { SetNewLanguagePrimaryRequestVNext } from '../models/SetNewLanguagePrimaryRequestVNext';
import { UpdateLanguagesRequestVNext } from '../models/UpdateLanguagesRequestVNext';
import { VersionContentFolder } from '../models/VersionContentFolder';
import { VersionPage } from '../models/VersionPage';
import { LandingPagesApiRequestFactory, LandingPagesApiResponseProcessor } from "../apis/LandingPagesApi";
export declare class PromiseLandingPagesApi {
    private api;
    constructor(configuration: Configuration, requestFactory?: LandingPagesApiRequestFactory, responseProcessor?: LandingPagesApiResponseProcessor);
    archiveWithHttpInfo(objectId: string, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    archive(objectId: string, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<void>;
    archiveBatchWithHttpInfo(batchInputString: BatchInputString, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    archiveBatch(batchInputString: BatchInputString, _options?: PromiseConfigurationOptions): Promise<void>;
    archiveFolderWithHttpInfo(objectId: string, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    archiveFolder(objectId: string, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<void>;
    archiveFoldersWithHttpInfo(batchInputString: BatchInputString, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    archiveFolders(batchInputString: BatchInputString, _options?: PromiseConfigurationOptions): Promise<void>;
    attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    attachToLangGroup(attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    cloneWithHttpInfo(contentCloneRequestVNext: ContentCloneRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    clone(contentCloneRequestVNext: ContentCloneRequestVNext, _options?: PromiseConfigurationOptions): Promise<Page>;
    createWithHttpInfo(page: Page, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void | Page>>;
    create(page: Page, _options?: PromiseConfigurationOptions): Promise<void | Page>;
    createABTestVariationWithHttpInfo(abTestCreateRequestVNext: AbTestCreateRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    createABTestVariation(abTestCreateRequestVNext: AbTestCreateRequestVNext, _options?: PromiseConfigurationOptions): Promise<Page>;
    createBatchWithHttpInfo(batchInputPage: BatchInputPage, _options?: PromiseConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    createBatch(batchInputPage: BatchInputPage, _options?: PromiseConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    createFolderWithHttpInfo(contentFolder: ContentFolder, _options?: PromiseConfigurationOptions): Promise<HttpInfo<ContentFolder>>;
    createFolder(contentFolder: ContentFolder, _options?: PromiseConfigurationOptions): Promise<ContentFolder>;
    createFoldersWithHttpInfo(batchInputContentFolder: BatchInputContentFolder, _options?: PromiseConfigurationOptions): Promise<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    createFolders(batchInputContentFolder: BatchInputContentFolder, _options?: PromiseConfigurationOptions): Promise<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>;
    createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    createLangVariation(contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext, _options?: PromiseConfigurationOptions): Promise<Page>;
    detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    detachFromLangGroup(detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    endActiveABTestWithHttpInfo(abTestEndRequestVNext: AbTestEndRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    endActiveABTest(abTestEndRequestVNext: AbTestEndRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    getByIdWithHttpInfo(objectId: string, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    getById(objectId: string, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<Page>;
    getDraftByIdWithHttpInfo(objectId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    getDraftById(objectId: string, _options?: PromiseConfigurationOptions): Promise<Page>;
    getFolderByIdWithHttpInfo(objectId: string, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<ContentFolder>>;
    getFolderById(objectId: string, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<ContentFolder>;
    getFolderPreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<VersionContentFolder>>;
    getFolderPreviousVersion(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<VersionContentFolder>;
    getFolderPreviousVersionsWithHttpInfo(objectId: string, after?: string, before?: string, limit?: number, _options?: PromiseConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalVersionContentFolder>>;
    getFolderPreviousVersions(objectId: string, after?: string, before?: string, limit?: number, _options?: PromiseConfigurationOptions): Promise<CollectionResponseWithTotalVersionContentFolder>;
    getFoldersPageWithHttpInfo(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalContentFolderForwardPaging>>;
    getFoldersPage(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<CollectionResponseWithTotalContentFolderForwardPaging>;
    getPageWithHttpInfo(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalPageForwardPaging>>;
    getPage(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<CollectionResponseWithTotalPageForwardPaging>;
    getPreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<VersionPage>>;
    getPreviousVersion(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<VersionPage>;
    getPreviousVersionsWithHttpInfo(objectId: string, after?: string, before?: string, limit?: number, _options?: PromiseConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalVersionPage>>;
    getPreviousVersions(objectId: string, after?: string, before?: string, limit?: number, _options?: PromiseConfigurationOptions): Promise<CollectionResponseWithTotalVersionPage>;
    pushLiveWithHttpInfo(objectId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    pushLive(objectId: string, _options?: PromiseConfigurationOptions): Promise<void>;
    readBatchWithHttpInfo(batchInputString: BatchInputString, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    readBatch(batchInputString: BatchInputString, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    readFoldersWithHttpInfo(batchInputString: BatchInputString, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    readFolders(batchInputString: BatchInputString, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>;
    rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext: AbTestRerunRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    rerunPreviousABTest(abTestRerunRequestVNext: AbTestRerunRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    resetDraftWithHttpInfo(objectId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    resetDraft(objectId: string, _options?: PromiseConfigurationOptions): Promise<void>;
    restoreFolderPreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<ContentFolder>>;
    restoreFolderPreviousVersion(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<ContentFolder>;
    restorePreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    restorePreviousVersion(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<Page>;
    restorePreviousVersionToDraftWithHttpInfo(objectId: string, revisionId: number, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    restorePreviousVersionToDraft(objectId: string, revisionId: number, _options?: PromiseConfigurationOptions): Promise<Page>;
    scheduleWithHttpInfo(contentScheduleRequestVNext: ContentScheduleRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    schedule(contentScheduleRequestVNext: ContentScheduleRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    setLangPrimary(setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    updateWithHttpInfo(objectId: string, page: Page, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    update(objectId: string, page: Page, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<Page>;
    updateBatchWithHttpInfo(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    updateBatch(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    updateDraftWithHttpInfo(objectId: string, page: Page, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    updateDraft(objectId: string, page: Page, _options?: PromiseConfigurationOptions): Promise<Page>;
    updateFolderWithHttpInfo(objectId: string, contentFolder: ContentFolder, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<ContentFolder>>;
    updateFolder(objectId: string, contentFolder: ContentFolder, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<ContentFolder>;
    updateFoldersWithHttpInfo(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    updateFolders(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>;
    updateLangsWithHttpInfo(updateLanguagesRequestVNext: UpdateLanguagesRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    updateLangs(updateLanguagesRequestVNext: UpdateLanguagesRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
}
import { SitePagesApiRequestFactory, SitePagesApiResponseProcessor } from "../apis/SitePagesApi";
export declare class PromiseSitePagesApi {
    private api;
    constructor(configuration: Configuration, requestFactory?: SitePagesApiRequestFactory, responseProcessor?: SitePagesApiResponseProcessor);
    archiveWithHttpInfo(objectId: string, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    archive(objectId: string, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<void>;
    archiveBatchWithHttpInfo(batchInputString: BatchInputString, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    archiveBatch(batchInputString: BatchInputString, _options?: PromiseConfigurationOptions): Promise<void>;
    attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    attachToLangGroup(attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    cloneWithHttpInfo(contentCloneRequestVNext: ContentCloneRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    clone(contentCloneRequestVNext: ContentCloneRequestVNext, _options?: PromiseConfigurationOptions): Promise<Page>;
    createWithHttpInfo(page: Page, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void | Page>>;
    create(page: Page, _options?: PromiseConfigurationOptions): Promise<void | Page>;
    createABTestVariationWithHttpInfo(abTestCreateRequestVNext: AbTestCreateRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    createABTestVariation(abTestCreateRequestVNext: AbTestCreateRequestVNext, _options?: PromiseConfigurationOptions): Promise<Page>;
    createBatchWithHttpInfo(batchInputPage: BatchInputPage, _options?: PromiseConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    createBatch(batchInputPage: BatchInputPage, _options?: PromiseConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    createLangVariation(contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext, _options?: PromiseConfigurationOptions): Promise<Page>;
    detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    detachFromLangGroup(detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    endActiveABTestWithHttpInfo(abTestEndRequestVNext: AbTestEndRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    endActiveABTest(abTestEndRequestVNext: AbTestEndRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    getByIdWithHttpInfo(objectId: string, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    getById(objectId: string, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<Page>;
    getDraftByIdWithHttpInfo(objectId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    getDraftById(objectId: string, _options?: PromiseConfigurationOptions): Promise<Page>;
    getPageWithHttpInfo(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalPageForwardPaging>>;
    getPage(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: PromiseConfigurationOptions): Promise<CollectionResponseWithTotalPageForwardPaging>;
    getPreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<VersionPage>>;
    getPreviousVersion(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<VersionPage>;
    getPreviousVersionsWithHttpInfo(objectId: string, after?: string, before?: string, limit?: number, _options?: PromiseConfigurationOptions): Promise<HttpInfo<CollectionResponseWithTotalVersionPage>>;
    getPreviousVersions(objectId: string, after?: string, before?: string, limit?: number, _options?: PromiseConfigurationOptions): Promise<CollectionResponseWithTotalVersionPage>;
    pushLiveWithHttpInfo(objectId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    pushLive(objectId: string, _options?: PromiseConfigurationOptions): Promise<void>;
    readBatchWithHttpInfo(batchInputString: BatchInputString, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    readBatch(batchInputString: BatchInputString, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext: AbTestRerunRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    rerunPreviousABTest(abTestRerunRequestVNext: AbTestRerunRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    resetDraftWithHttpInfo(objectId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    resetDraft(objectId: string, _options?: PromiseConfigurationOptions): Promise<void>;
    restorePreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    restorePreviousVersion(objectId: string, revisionId: string, _options?: PromiseConfigurationOptions): Promise<Page>;
    restorePreviousVersionToDraftWithHttpInfo(objectId: string, revisionId: number, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    restorePreviousVersionToDraft(objectId: string, revisionId: number, _options?: PromiseConfigurationOptions): Promise<Page>;
    scheduleWithHttpInfo(contentScheduleRequestVNext: ContentScheduleRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    schedule(contentScheduleRequestVNext: ContentScheduleRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    setLangPrimary(setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
    updateWithHttpInfo(objectId: string, page: Page, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    update(objectId: string, page: Page, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<Page>;
    updateBatchWithHttpInfo(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    updateBatch(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: PromiseConfigurationOptions): Promise<BatchResponsePage | BatchResponsePageWithErrors>;
    updateDraftWithHttpInfo(objectId: string, page: Page, _options?: PromiseConfigurationOptions): Promise<HttpInfo<Page>>;
    updateDraft(objectId: string, page: Page, _options?: PromiseConfigurationOptions): Promise<Page>;
    updateLangsWithHttpInfo(updateLanguagesRequestVNext: UpdateLanguagesRequestVNext, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    updateLangs(updateLanguagesRequestVNext: UpdateLanguagesRequestVNext, _options?: PromiseConfigurationOptions): Promise<void>;
}
