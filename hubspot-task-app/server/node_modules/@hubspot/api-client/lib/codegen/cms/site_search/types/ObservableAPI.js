"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObservablePublicApi = void 0;
const rxjsStub_1 = require("../rxjsStub");
const rxjsStub_2 = require("../rxjsStub");
const PublicApi_1 = require("../apis/PublicApi");
class ObservablePublicApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.configuration = configuration;
        this.requestFactory = requestFactory || new PublicApi_1.PublicApiRequestFactory(configuration);
        this.responseProcessor = responseProcessor || new PublicApi_1.PublicApiResponseProcessor();
    }
    getByIdWithHttpInfo(contentId, type, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.getById(contentId, type, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.getByIdWithHttpInfo(rsp)));
        }));
    }
    getById(contentId, type, _options) {
        return this.getByIdWithHttpInfo(contentId, type, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
    searchWithHttpInfo(q, limit, offset, language, matchPrefix, autocomplete, popularityBoost, boostLimit, boostRecent, tableId, hubdbQuery, domain, type, pathPrefix, property, length, groupId, _options) {
        let _config = this.configuration;
        let allMiddleware = [];
        if (_options && _options.middleware) {
            const middlewareMergeStrategy = _options.middlewareMergeStrategy || 'replace';
            const calltimeMiddleware = _options.middleware;
            switch (middlewareMergeStrategy) {
                case 'append':
                    allMiddleware = this.configuration.middleware.concat(calltimeMiddleware);
                    break;
                case 'prepend':
                    allMiddleware = calltimeMiddleware.concat(this.configuration.middleware);
                    break;
                case 'replace':
                    allMiddleware = calltimeMiddleware;
                    break;
                default:
                    throw new Error(`unrecognized middleware merge strategy '${middlewareMergeStrategy}'`);
            }
        }
        if (_options) {
            _config = {
                baseServer: _options.baseServer || this.configuration.baseServer,
                httpApi: _options.httpApi || this.configuration.httpApi,
                authMethods: _options.authMethods || this.configuration.authMethods,
                middleware: allMiddleware || this.configuration.middleware
            };
        }
        const requestContextPromise = this.requestFactory.search(q, limit, offset, language, matchPrefix, autocomplete, popularityBoost, boostLimit, boostRecent, tableId, hubdbQuery, domain, type, pathPrefix, property, length, groupId, _config);
        let middlewarePreObservable = (0, rxjsStub_1.from)(requestContextPromise);
        for (const middleware of allMiddleware) {
            middlewarePreObservable = middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => middleware.pre(ctx)));
        }
        return middlewarePreObservable.pipe((0, rxjsStub_2.mergeMap)((ctx) => this.configuration.httpApi.send(ctx))).
            pipe((0, rxjsStub_2.mergeMap)((response) => {
            let middlewarePostObservable = (0, rxjsStub_1.of)(response);
            for (const middleware of allMiddleware.reverse()) {
                middlewarePostObservable = middlewarePostObservable.pipe((0, rxjsStub_2.mergeMap)((rsp) => middleware.post(rsp)));
            }
            return middlewarePostObservable.pipe((0, rxjsStub_2.map)((rsp) => this.responseProcessor.searchWithHttpInfo(rsp)));
        }));
    }
    search(q, limit, offset, language, matchPrefix, autocomplete, popularityBoost, boostLimit, boostRecent, tableId, hubdbQuery, domain, type, pathPrefix, property, length, groupId, _options) {
        return this.searchWithHttpInfo(q, limit, offset, language, matchPrefix, autocomplete, popularityBoost, boostLimit, boostRecent, tableId, hubdbQuery, domain, type, pathPrefix, property, length, groupId, _options).pipe((0, rxjsStub_2.map)((apiResponse) => apiResponse.data));
    }
}
exports.ObservablePublicApi = ObservablePublicApi;
//# sourceMappingURL=ObservableAPI.js.map