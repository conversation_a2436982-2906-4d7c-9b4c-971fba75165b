"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchResponseContentFolderStatusEnum = exports.BatchResponseContentFolder = void 0;
class BatchResponseContentFolder {
    static getAttributeTypeMap() {
        return BatchResponseContentFolder.attributeTypeMap;
    }
    constructor() {
    }
}
exports.BatchResponseContentFolder = BatchResponseContentFolder;
BatchResponseContentFolder.discriminator = undefined;
BatchResponseContentFolder.mapping = undefined;
BatchResponseContentFolder.attributeTypeMap = [
    {
        "name": "completedAt",
        "baseName": "completedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "requestedAt",
        "baseName": "requestedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "startedAt",
        "baseName": "startedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "links",
        "baseName": "links",
        "type": "{ [key: string]: string; }",
        "format": ""
    },
    {
        "name": "results",
        "baseName": "results",
        "type": "Array<ContentFolder>",
        "format": ""
    },
    {
        "name": "status",
        "baseName": "status",
        "type": "BatchResponseContentFolderStatusEnum",
        "format": ""
    }
];
var BatchResponseContentFolderStatusEnum;
(function (BatchResponseContentFolderStatusEnum) {
    BatchResponseContentFolderStatusEnum["Pending"] = "PENDING";
    BatchResponseContentFolderStatusEnum["Processing"] = "PROCESSING";
    BatchResponseContentFolderStatusEnum["Canceled"] = "CANCELED";
    BatchResponseContentFolderStatusEnum["Complete"] = "COMPLETE";
})(BatchResponseContentFolderStatusEnum = exports.BatchResponseContentFolderStatusEnum || (exports.BatchResponseContentFolderStatusEnum = {}));
//# sourceMappingURL=BatchResponseContentFolder.js.map