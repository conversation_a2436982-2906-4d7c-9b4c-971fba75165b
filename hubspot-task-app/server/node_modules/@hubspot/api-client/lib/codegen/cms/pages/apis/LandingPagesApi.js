"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LandingPagesApiResponseProcessor = exports.LandingPagesApiRequestFactory = void 0;
const baseapi_1 = require("./baseapi");
const http_1 = require("../http/http");
const ObjectSerializer_1 = require("../models/ObjectSerializer");
const exception_1 = require("./exception");
const util_1 = require("../util");
class LandingPagesApiRequestFactory extends baseapi_1.BaseAPIRequestFactory {
    archive(objectId, archived, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "archive", "objectId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.DELETE);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    archiveBatch(batchInputString, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (batchInputString === null || batchInputString === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "archiveBatch", "batchInputString");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/batch/archive';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(batchInputString, "BatchInputString", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    archiveFolder(objectId, archived, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "archiveFolder", "objectId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/{objectId}'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.DELETE);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    archiveFolders(batchInputString, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (batchInputString === null || batchInputString === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "archiveFolders", "batchInputString");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/batch/archive';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(batchInputString, "BatchInputString", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    attachToLangGroup(attachToLangPrimaryRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (attachToLangPrimaryRequestVNext === null || attachToLangPrimaryRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "attachToLangGroup", "attachToLangPrimaryRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/multi-language/attach-to-lang-group';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(attachToLangPrimaryRequestVNext, "AttachToLangPrimaryRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    clone(contentCloneRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (contentCloneRequestVNext === null || contentCloneRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "clone", "contentCloneRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/clone';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(contentCloneRequestVNext, "ContentCloneRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    create(page, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (page === null || page === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "create", "page");
            }
            const localVarPath = '/cms/v3/pages/landing-pages';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(page, "Page", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    createABTestVariation(abTestCreateRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (abTestCreateRequestVNext === null || abTestCreateRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "createABTestVariation", "abTestCreateRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/ab-test/create-variation';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(abTestCreateRequestVNext, "AbTestCreateRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    createBatch(batchInputPage, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (batchInputPage === null || batchInputPage === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "createBatch", "batchInputPage");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/batch/create';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(batchInputPage, "BatchInputPage", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    createFolder(contentFolder, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (contentFolder === null || contentFolder === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "createFolder", "contentFolder");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(contentFolder, "ContentFolder", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    createFolders(batchInputContentFolder, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (batchInputContentFolder === null || batchInputContentFolder === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "createFolders", "batchInputContentFolder");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/batch/create';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(batchInputContentFolder, "BatchInputContentFolder", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    createLangVariation(contentLanguageCloneRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (contentLanguageCloneRequestVNext === null || contentLanguageCloneRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "createLangVariation", "contentLanguageCloneRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/multi-language/create-language-variation';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(contentLanguageCloneRequestVNext, "ContentLanguageCloneRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    detachFromLangGroup(detachFromLangGroupRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (detachFromLangGroupRequestVNext === null || detachFromLangGroupRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "detachFromLangGroup", "detachFromLangGroupRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/multi-language/detach-from-lang-group';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(detachFromLangGroupRequestVNext, "DetachFromLangGroupRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    endActiveABTest(abTestEndRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (abTestEndRequestVNext === null || abTestEndRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "endActiveABTest", "abTestEndRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/ab-test/end';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(abTestEndRequestVNext, "AbTestEndRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getById(objectId, archived, property, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "getById", "objectId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            if (property !== undefined) {
                requestContext.setQueryParam("property", ObjectSerializer_1.ObjectSerializer.serialize(property, "string", ""));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getDraftById(objectId, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "getDraftById", "objectId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}/draft'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getFolderById(objectId, archived, property, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "getFolderById", "objectId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/{objectId}'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            if (property !== undefined) {
                requestContext.setQueryParam("property", ObjectSerializer_1.ObjectSerializer.serialize(property, "string", ""));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getFolderPreviousVersion(objectId, revisionId, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "getFolderPreviousVersion", "objectId");
            }
            if (revisionId === null || revisionId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "getFolderPreviousVersion", "revisionId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/{objectId}/revisions/{revisionId}'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)))
                .replace('{' + 'revisionId' + '}', encodeURIComponent(String(revisionId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getFolderPreviousVersions(objectId, after, before, limit, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "getFolderPreviousVersions", "objectId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/{objectId}/revisions'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (after !== undefined) {
                requestContext.setQueryParam("after", ObjectSerializer_1.ObjectSerializer.serialize(after, "string", ""));
            }
            if (before !== undefined) {
                requestContext.setQueryParam("before", ObjectSerializer_1.ObjectSerializer.serialize(before, "string", ""));
            }
            if (limit !== undefined) {
                requestContext.setQueryParam("limit", ObjectSerializer_1.ObjectSerializer.serialize(limit, "number", "int32"));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getFoldersPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            const localVarPath = '/cms/v3/pages/landing-pages/folders';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (createdAt !== undefined) {
                requestContext.setQueryParam("createdAt", ObjectSerializer_1.ObjectSerializer.serialize(createdAt, "Date", "date-time"));
            }
            if (createdAfter !== undefined) {
                requestContext.setQueryParam("createdAfter", ObjectSerializer_1.ObjectSerializer.serialize(createdAfter, "Date", "date-time"));
            }
            if (createdBefore !== undefined) {
                requestContext.setQueryParam("createdBefore", ObjectSerializer_1.ObjectSerializer.serialize(createdBefore, "Date", "date-time"));
            }
            if (updatedAt !== undefined) {
                requestContext.setQueryParam("updatedAt", ObjectSerializer_1.ObjectSerializer.serialize(updatedAt, "Date", "date-time"));
            }
            if (updatedAfter !== undefined) {
                requestContext.setQueryParam("updatedAfter", ObjectSerializer_1.ObjectSerializer.serialize(updatedAfter, "Date", "date-time"));
            }
            if (updatedBefore !== undefined) {
                requestContext.setQueryParam("updatedBefore", ObjectSerializer_1.ObjectSerializer.serialize(updatedBefore, "Date", "date-time"));
            }
            if (sort !== undefined) {
                const serializedParams = ObjectSerializer_1.ObjectSerializer.serialize(sort, "Array<string>", "");
                for (const serializedParam of serializedParams) {
                    requestContext.appendQueryParam("sort", serializedParam);
                }
            }
            if (after !== undefined) {
                requestContext.setQueryParam("after", ObjectSerializer_1.ObjectSerializer.serialize(after, "string", ""));
            }
            if (limit !== undefined) {
                requestContext.setQueryParam("limit", ObjectSerializer_1.ObjectSerializer.serialize(limit, "number", "int32"));
            }
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            if (property !== undefined) {
                requestContext.setQueryParam("property", ObjectSerializer_1.ObjectSerializer.serialize(property, "string", ""));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getPage(createdAt, createdAfter, createdBefore, updatedAt, updatedAfter, updatedBefore, sort, after, limit, archived, property, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            const localVarPath = '/cms/v3/pages/landing-pages';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (createdAt !== undefined) {
                requestContext.setQueryParam("createdAt", ObjectSerializer_1.ObjectSerializer.serialize(createdAt, "Date", "date-time"));
            }
            if (createdAfter !== undefined) {
                requestContext.setQueryParam("createdAfter", ObjectSerializer_1.ObjectSerializer.serialize(createdAfter, "Date", "date-time"));
            }
            if (createdBefore !== undefined) {
                requestContext.setQueryParam("createdBefore", ObjectSerializer_1.ObjectSerializer.serialize(createdBefore, "Date", "date-time"));
            }
            if (updatedAt !== undefined) {
                requestContext.setQueryParam("updatedAt", ObjectSerializer_1.ObjectSerializer.serialize(updatedAt, "Date", "date-time"));
            }
            if (updatedAfter !== undefined) {
                requestContext.setQueryParam("updatedAfter", ObjectSerializer_1.ObjectSerializer.serialize(updatedAfter, "Date", "date-time"));
            }
            if (updatedBefore !== undefined) {
                requestContext.setQueryParam("updatedBefore", ObjectSerializer_1.ObjectSerializer.serialize(updatedBefore, "Date", "date-time"));
            }
            if (sort !== undefined) {
                const serializedParams = ObjectSerializer_1.ObjectSerializer.serialize(sort, "Array<string>", "");
                for (const serializedParam of serializedParams) {
                    requestContext.appendQueryParam("sort", serializedParam);
                }
            }
            if (after !== undefined) {
                requestContext.setQueryParam("after", ObjectSerializer_1.ObjectSerializer.serialize(after, "string", ""));
            }
            if (limit !== undefined) {
                requestContext.setQueryParam("limit", ObjectSerializer_1.ObjectSerializer.serialize(limit, "number", "int32"));
            }
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            if (property !== undefined) {
                requestContext.setQueryParam("property", ObjectSerializer_1.ObjectSerializer.serialize(property, "string", ""));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getPreviousVersion(objectId, revisionId, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "getPreviousVersion", "objectId");
            }
            if (revisionId === null || revisionId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "getPreviousVersion", "revisionId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}/revisions/{revisionId}'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)))
                .replace('{' + 'revisionId' + '}', encodeURIComponent(String(revisionId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getPreviousVersions(objectId, after, before, limit, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "getPreviousVersions", "objectId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}/revisions'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (after !== undefined) {
                requestContext.setQueryParam("after", ObjectSerializer_1.ObjectSerializer.serialize(after, "string", ""));
            }
            if (before !== undefined) {
                requestContext.setQueryParam("before", ObjectSerializer_1.ObjectSerializer.serialize(before, "string", ""));
            }
            if (limit !== undefined) {
                requestContext.setQueryParam("limit", ObjectSerializer_1.ObjectSerializer.serialize(limit, "number", "int32"));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    pushLive(objectId, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "pushLive", "objectId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}/draft/push-live'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    readBatch(batchInputString, archived, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (batchInputString === null || batchInputString === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "readBatch", "batchInputString");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/batch/read';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(batchInputString, "BatchInputString", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    readFolders(batchInputString, archived, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (batchInputString === null || batchInputString === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "readFolders", "batchInputString");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/batch/read';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(batchInputString, "BatchInputString", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    rerunPreviousABTest(abTestRerunRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (abTestRerunRequestVNext === null || abTestRerunRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "rerunPreviousABTest", "abTestRerunRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/ab-test/rerun';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(abTestRerunRequestVNext, "AbTestRerunRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    resetDraft(objectId, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "resetDraft", "objectId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}/draft/reset'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    restoreFolderPreviousVersion(objectId, revisionId, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "restoreFolderPreviousVersion", "objectId");
            }
            if (revisionId === null || revisionId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "restoreFolderPreviousVersion", "revisionId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/{objectId}/revisions/{revisionId}/restore'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)))
                .replace('{' + 'revisionId' + '}', encodeURIComponent(String(revisionId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    restorePreviousVersion(objectId, revisionId, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "restorePreviousVersion", "objectId");
            }
            if (revisionId === null || revisionId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "restorePreviousVersion", "revisionId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}/revisions/{revisionId}/restore'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)))
                .replace('{' + 'revisionId' + '}', encodeURIComponent(String(revisionId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    restorePreviousVersionToDraft(objectId, revisionId, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "restorePreviousVersionToDraft", "objectId");
            }
            if (revisionId === null || revisionId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "restorePreviousVersionToDraft", "revisionId");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}/revisions/{revisionId}/restore-to-draft'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)))
                .replace('{' + 'revisionId' + '}', encodeURIComponent(String(revisionId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    schedule(contentScheduleRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (contentScheduleRequestVNext === null || contentScheduleRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "schedule", "contentScheduleRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/schedule';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(contentScheduleRequestVNext, "ContentScheduleRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    setLangPrimary(setNewLanguagePrimaryRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (setNewLanguagePrimaryRequestVNext === null || setNewLanguagePrimaryRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "setLangPrimary", "setNewLanguagePrimaryRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/multi-language/set-new-lang-primary';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.PUT);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(setNewLanguagePrimaryRequestVNext, "SetNewLanguagePrimaryRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    update(objectId, page, archived, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "update", "objectId");
            }
            if (page === null || page === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "update", "page");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.PATCH);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(page, "Page", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    updateBatch(batchInputJsonNode, archived, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (batchInputJsonNode === null || batchInputJsonNode === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "updateBatch", "batchInputJsonNode");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/batch/update';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(batchInputJsonNode, "BatchInputJsonNode", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    updateDraft(objectId, page, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "updateDraft", "objectId");
            }
            if (page === null || page === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "updateDraft", "page");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/{objectId}/draft'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.PATCH);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(page, "Page", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    updateFolder(objectId, contentFolder, archived, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (objectId === null || objectId === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "updateFolder", "objectId");
            }
            if (contentFolder === null || contentFolder === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "updateFolder", "contentFolder");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/{objectId}'
                .replace('{' + 'objectId' + '}', encodeURIComponent(String(objectId)));
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.PATCH);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(contentFolder, "ContentFolder", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    updateFolders(batchInputJsonNode, archived, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (batchInputJsonNode === null || batchInputJsonNode === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "updateFolders", "batchInputJsonNode");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/folders/batch/update';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (archived !== undefined) {
                requestContext.setQueryParam("archived", ObjectSerializer_1.ObjectSerializer.serialize(archived, "boolean", ""));
            }
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(batchInputJsonNode, "BatchInputJsonNode", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    updateLangs(updateLanguagesRequestVNext, _options) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            if (updateLanguagesRequestVNext === null || updateLanguagesRequestVNext === undefined) {
                throw new baseapi_1.RequiredError("LandingPagesApi", "updateLangs", "updateLanguagesRequestVNext");
            }
            const localVarPath = '/cms/v3/pages/landing-pages/multi-language/update-languages';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.POST);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            const contentType = ObjectSerializer_1.ObjectSerializer.getPreferredMediaType([
                "application/json"
            ]);
            requestContext.setHeaderParam("Content-Type", contentType);
            const serializedBody = ObjectSerializer_1.ObjectSerializer.stringify(ObjectSerializer_1.ObjectSerializer.serialize(updateLanguagesRequestVNext, "UpdateLanguagesRequestVNext", ""), contentType);
            requestContext.setBody(serializedBody);
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = (_a = _config === null || _config === void 0 ? void 0 : _config.authMethods) === null || _a === void 0 ? void 0 : _a.default;
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
}
exports.LandingPagesApiRequestFactory = LandingPagesApiRequestFactory;
class LandingPagesApiResponseProcessor {
    archiveWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    archiveBatchWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    archiveFolderWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    archiveFoldersWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    attachToLangGroupWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    cloneWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    createWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("201", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void | Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    createABTestVariationWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("201", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    createBatchWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("201", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponsePage", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("207", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponsePageWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponsePage | BatchResponsePageWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    createFolderWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("201", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "ContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "ContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    createFoldersWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("201", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponseContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("207", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponseContentFolderWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponseContentFolder | BatchResponseContentFolderWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    createLangVariationWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    detachFromLangGroupWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    endActiveABTestWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getByIdWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getDraftByIdWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getFolderByIdWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "ContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "ContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getFolderPreviousVersionWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "VersionContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "VersionContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getFolderPreviousVersionsWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "CollectionResponseWithTotalVersionContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "CollectionResponseWithTotalVersionContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getFoldersPageWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "CollectionResponseWithTotalContentFolderForwardPaging", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "CollectionResponseWithTotalContentFolderForwardPaging", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getPageWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "CollectionResponseWithTotalPageForwardPaging", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "CollectionResponseWithTotalPageForwardPaging", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getPreviousVersionWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "VersionPage", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "VersionPage", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getPreviousVersionsWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "CollectionResponseWithTotalVersionPage", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "CollectionResponseWithTotalVersionPage", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    pushLiveWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    readBatchWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponsePage", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("207", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponsePageWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponsePage | BatchResponsePageWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    readFoldersWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponseContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("207", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponseContentFolderWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponseContentFolder | BatchResponseContentFolderWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    rerunPreviousABTestWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    resetDraftWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    restoreFolderPreviousVersionWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "ContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "ContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    restorePreviousVersionWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    restorePreviousVersionToDraftWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    scheduleWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    setLangPrimaryWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("204", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    updateWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    updateBatchWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponsePage", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("207", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponsePageWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponsePage | BatchResponsePageWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    updateDraftWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Page", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    updateFolderWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "ContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "ContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    updateFoldersWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponseContentFolder", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("207", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponseContentFolderWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "BatchResponseContentFolder | BatchResponseContentFolderWithErrors", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    updateLangsWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, undefined);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "void", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
}
exports.LandingPagesApiResponseProcessor = LandingPagesApiResponseProcessor;
//# sourceMappingURL=LandingPagesApi.js.map