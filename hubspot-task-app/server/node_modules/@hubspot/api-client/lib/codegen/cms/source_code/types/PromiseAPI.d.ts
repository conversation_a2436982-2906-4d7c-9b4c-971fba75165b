import { HttpFile, HttpInfo } from '../http/http';
import { Configuration, PromiseConfigurationOptions } from '../configuration';
import { ActionResponse } from '../models/ActionResponse';
import { AssetFileMetadata } from '../models/AssetFileMetadata';
import { FileExtractRequest } from '../models/FileExtractRequest';
import { TaskLocator } from '../models/TaskLocator';
import { ContentApiRequestFactory, ContentApiResponseProcessor } from "../apis/ContentApi";
export declare class PromiseContentApi {
    private api;
    constructor(configuration: Configuration, requestFactory?: ContentApiRequestFactory, responseProcessor?: ContentApiResponseProcessor);
    archiveWithHttpInfo(environment: string, path: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    archive(environment: string, path: string, _options?: PromiseConfigurationOptions): Promise<void>;
    createWithHttpInfo(environment: string, path: string, file?: HttpFile, _options?: PromiseConfigurationOptions): Promise<HttpInfo<AssetFileMetadata>>;
    create(environment: string, path: string, file?: HttpFile, _options?: PromiseConfigurationOptions): Promise<AssetFileMetadata>;
    createOrUpdateWithHttpInfo(environment: string, path: string, file?: HttpFile, _options?: PromiseConfigurationOptions): Promise<HttpInfo<AssetFileMetadata>>;
    createOrUpdate(environment: string, path: string, file?: HttpFile, _options?: PromiseConfigurationOptions): Promise<AssetFileMetadata>;
    downloadWithHttpInfo(environment: string, path: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<HttpFile>>;
    download(environment: string, path: string, _options?: PromiseConfigurationOptions): Promise<HttpFile>;
}
import { ExtractApiRequestFactory, ExtractApiResponseProcessor } from "../apis/ExtractApi";
export declare class PromiseExtractApi {
    private api;
    constructor(configuration: Configuration, requestFactory?: ExtractApiRequestFactory, responseProcessor?: ExtractApiResponseProcessor);
    doAsyncWithHttpInfo(fileExtractRequest: FileExtractRequest, _options?: PromiseConfigurationOptions): Promise<HttpInfo<TaskLocator>>;
    doAsync(fileExtractRequest: FileExtractRequest, _options?: PromiseConfigurationOptions): Promise<TaskLocator>;
    getAsyncStatusWithHttpInfo(taskId: number, _options?: PromiseConfigurationOptions): Promise<HttpInfo<ActionResponse>>;
    getAsyncStatus(taskId: number, _options?: PromiseConfigurationOptions): Promise<ActionResponse>;
}
import { MetadataApiRequestFactory, MetadataApiResponseProcessor } from "../apis/MetadataApi";
export declare class PromiseMetadataApi {
    private api;
    constructor(configuration: Configuration, requestFactory?: MetadataApiRequestFactory, responseProcessor?: MetadataApiResponseProcessor);
    getWithHttpInfo(environment: string, path: string, properties?: string, _options?: PromiseConfigurationOptions): Promise<HttpInfo<AssetFileMetadata>>;
    get(environment: string, path: string, properties?: string, _options?: PromiseConfigurationOptions): Promise<AssetFileMetadata>;
}
import { ValidationApiRequestFactory, ValidationApiResponseProcessor } from "../apis/ValidationApi";
export declare class PromiseValidationApi {
    private api;
    constructor(configuration: Configuration, requestFactory?: ValidationApiRequestFactory, responseProcessor?: ValidationApiResponseProcessor);
    doValidateWithHttpInfo(path: string, file?: HttpFile, _options?: PromiseConfigurationOptions): Promise<HttpInfo<void>>;
    doValidate(path: string, file?: HttpFile, _options?: PromiseConfigurationOptions): Promise<void>;
}
