"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageCurrentStateEnum = exports.PageAbStatusEnum = exports.PageContentTypeCategoryEnum = exports.PageLanguageEnum = exports.Page = void 0;
class Page {
    static getAttributeTypeMap() {
        return Page.attributeTypeMap;
    }
    constructor() {
    }
}
exports.Page = Page;
Page.discriminator = undefined;
Page.mapping = undefined;
Page.attributeTypeMap = [
    {
        "name": "publishDate",
        "baseName": "publishDate",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "language",
        "baseName": "language",
        "type": "PageLanguageEnum",
        "format": ""
    },
    {
        "name": "enableLayoutStylesheets",
        "baseName": "enableLayoutStylesheets",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "metaDescription",
        "baseName": "metaDescription",
        "type": "string",
        "format": ""
    },
    {
        "name": "attachedStylesheets",
        "baseName": "attachedStylesheets",
        "type": "Array<{ [key: string]: any; }>",
        "format": ""
    },
    {
        "name": "password",
        "baseName": "password",
        "type": "string",
        "format": ""
    },
    {
        "name": "publishImmediately",
        "baseName": "publishImmediately",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "htmlTitle",
        "baseName": "htmlTitle",
        "type": "string",
        "format": ""
    },
    {
        "name": "translations",
        "baseName": "translations",
        "type": "{ [key: string]: ContentLanguageVariation; }",
        "format": ""
    },
    {
        "name": "id",
        "baseName": "id",
        "type": "string",
        "format": ""
    },
    {
        "name": "state",
        "baseName": "state",
        "type": "string",
        "format": ""
    },
    {
        "name": "slug",
        "baseName": "slug",
        "type": "string",
        "format": ""
    },
    {
        "name": "createdById",
        "baseName": "createdById",
        "type": "string",
        "format": ""
    },
    {
        "name": "currentlyPublished",
        "baseName": "currentlyPublished",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "archivedInDashboard",
        "baseName": "archivedInDashboard",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "created",
        "baseName": "created",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "contentTypeCategory",
        "baseName": "contentTypeCategory",
        "type": "PageContentTypeCategoryEnum",
        "format": ""
    },
    {
        "name": "mabExperimentId",
        "baseName": "mabExperimentId",
        "type": "string",
        "format": ""
    },
    {
        "name": "updatedById",
        "baseName": "updatedById",
        "type": "string",
        "format": ""
    },
    {
        "name": "translatedFromId",
        "baseName": "translatedFromId",
        "type": "string",
        "format": ""
    },
    {
        "name": "folderId",
        "baseName": "folderId",
        "type": "string",
        "format": ""
    },
    {
        "name": "widgetContainers",
        "baseName": "widgetContainers",
        "type": "{ [key: string]: any; }",
        "format": ""
    },
    {
        "name": "pageExpiryRedirectId",
        "baseName": "pageExpiryRedirectId",
        "type": "number",
        "format": "int64"
    },
    {
        "name": "dynamicPageDataSourceType",
        "baseName": "dynamicPageDataSourceType",
        "type": "number",
        "format": "int32"
    },
    {
        "name": "featuredImage",
        "baseName": "featuredImage",
        "type": "string",
        "format": ""
    },
    {
        "name": "authorName",
        "baseName": "authorName",
        "type": "string",
        "format": ""
    },
    {
        "name": "domain",
        "baseName": "domain",
        "type": "string",
        "format": ""
    },
    {
        "name": "name",
        "baseName": "name",
        "type": "string",
        "format": ""
    },
    {
        "name": "dynamicPageHubDbTableId",
        "baseName": "dynamicPageHubDbTableId",
        "type": "string",
        "format": ""
    },
    {
        "name": "campaign",
        "baseName": "campaign",
        "type": "string",
        "format": ""
    },
    {
        "name": "dynamicPageDataSourceId",
        "baseName": "dynamicPageDataSourceId",
        "type": "string",
        "format": ""
    },
    {
        "name": "enableDomainStylesheets",
        "baseName": "enableDomainStylesheets",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "includeDefaultCustomCss",
        "baseName": "includeDefaultCustomCss",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "subcategory",
        "baseName": "subcategory",
        "type": "string",
        "format": ""
    },
    {
        "name": "layoutSections",
        "baseName": "layoutSections",
        "type": "{ [key: string]: LayoutSection; }",
        "format": ""
    },
    {
        "name": "updated",
        "baseName": "updated",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "footerHtml",
        "baseName": "footerHtml",
        "type": "string",
        "format": ""
    },
    {
        "name": "widgets",
        "baseName": "widgets",
        "type": "{ [key: string]: any; }",
        "format": ""
    },
    {
        "name": "headHtml",
        "baseName": "headHtml",
        "type": "string",
        "format": ""
    },
    {
        "name": "pageExpiryRedirectUrl",
        "baseName": "pageExpiryRedirectUrl",
        "type": "string",
        "format": ""
    },
    {
        "name": "abStatus",
        "baseName": "abStatus",
        "type": "PageAbStatusEnum",
        "format": ""
    },
    {
        "name": "useFeaturedImage",
        "baseName": "useFeaturedImage",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "abTestId",
        "baseName": "abTestId",
        "type": "string",
        "format": ""
    },
    {
        "name": "featuredImageAltText",
        "baseName": "featuredImageAltText",
        "type": "string",
        "format": ""
    },
    {
        "name": "contentGroupId",
        "baseName": "contentGroupId",
        "type": "string",
        "format": ""
    },
    {
        "name": "pageExpiryEnabled",
        "baseName": "pageExpiryEnabled",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "templatePath",
        "baseName": "templatePath",
        "type": "string",
        "format": ""
    },
    {
        "name": "url",
        "baseName": "url",
        "type": "string",
        "format": ""
    },
    {
        "name": "publicAccessRules",
        "baseName": "publicAccessRules",
        "type": "Array<any>",
        "format": ""
    },
    {
        "name": "archivedAt",
        "baseName": "archivedAt",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "themeSettingsValues",
        "baseName": "themeSettingsValues",
        "type": "{ [key: string]: any; }",
        "format": ""
    },
    {
        "name": "pageExpiryDate",
        "baseName": "pageExpiryDate",
        "type": "number",
        "format": "int64"
    },
    {
        "name": "publicAccessRulesEnabled",
        "baseName": "publicAccessRulesEnabled",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "pageRedirected",
        "baseName": "pageRedirected",
        "type": "boolean",
        "format": ""
    },
    {
        "name": "currentState",
        "baseName": "currentState",
        "type": "PageCurrentStateEnum",
        "format": ""
    },
    {
        "name": "categoryId",
        "baseName": "categoryId",
        "type": "number",
        "format": "int32"
    },
    {
        "name": "linkRelCanonicalUrl",
        "baseName": "linkRelCanonicalUrl",
        "type": "string",
        "format": ""
    }
];
var PageLanguageEnum;
(function (PageLanguageEnum) {
    PageLanguageEnum["Af"] = "af";
    PageLanguageEnum["AfNa"] = "af-na";
    PageLanguageEnum["AfZa"] = "af-za";
    PageLanguageEnum["Agq"] = "agq";
    PageLanguageEnum["AgqCm"] = "agq-cm";
    PageLanguageEnum["Ak"] = "ak";
    PageLanguageEnum["AkGh"] = "ak-gh";
    PageLanguageEnum["Am"] = "am";
    PageLanguageEnum["AmEt"] = "am-et";
    PageLanguageEnum["Ar"] = "ar";
    PageLanguageEnum["Ar001"] = "ar-001";
    PageLanguageEnum["ArAe"] = "ar-ae";
    PageLanguageEnum["ArBh"] = "ar-bh";
    PageLanguageEnum["ArDj"] = "ar-dj";
    PageLanguageEnum["ArDz"] = "ar-dz";
    PageLanguageEnum["ArEg"] = "ar-eg";
    PageLanguageEnum["ArEh"] = "ar-eh";
    PageLanguageEnum["ArEr"] = "ar-er";
    PageLanguageEnum["ArIl"] = "ar-il";
    PageLanguageEnum["ArIq"] = "ar-iq";
    PageLanguageEnum["ArJo"] = "ar-jo";
    PageLanguageEnum["ArKm"] = "ar-km";
    PageLanguageEnum["ArKw"] = "ar-kw";
    PageLanguageEnum["ArLb"] = "ar-lb";
    PageLanguageEnum["ArLy"] = "ar-ly";
    PageLanguageEnum["ArMa"] = "ar-ma";
    PageLanguageEnum["ArMr"] = "ar-mr";
    PageLanguageEnum["ArOm"] = "ar-om";
    PageLanguageEnum["ArPs"] = "ar-ps";
    PageLanguageEnum["ArQa"] = "ar-qa";
    PageLanguageEnum["ArSa"] = "ar-sa";
    PageLanguageEnum["ArSd"] = "ar-sd";
    PageLanguageEnum["ArSo"] = "ar-so";
    PageLanguageEnum["ArSs"] = "ar-ss";
    PageLanguageEnum["ArSy"] = "ar-sy";
    PageLanguageEnum["ArTd"] = "ar-td";
    PageLanguageEnum["ArTn"] = "ar-tn";
    PageLanguageEnum["ArYe"] = "ar-ye";
    PageLanguageEnum["As"] = "as";
    PageLanguageEnum["AsIn"] = "as-in";
    PageLanguageEnum["Asa"] = "asa";
    PageLanguageEnum["AsaTz"] = "asa-tz";
    PageLanguageEnum["Ast"] = "ast";
    PageLanguageEnum["AstEs"] = "ast-es";
    PageLanguageEnum["Az"] = "az";
    PageLanguageEnum["AzAz"] = "az-az";
    PageLanguageEnum["Bas"] = "bas";
    PageLanguageEnum["BasCm"] = "bas-cm";
    PageLanguageEnum["Be"] = "be";
    PageLanguageEnum["BeBy"] = "be-by";
    PageLanguageEnum["Bem"] = "bem";
    PageLanguageEnum["BemZm"] = "bem-zm";
    PageLanguageEnum["Bez"] = "bez";
    PageLanguageEnum["BezTz"] = "bez-tz";
    PageLanguageEnum["Bg"] = "bg";
    PageLanguageEnum["BgBg"] = "bg-bg";
    PageLanguageEnum["Bm"] = "bm";
    PageLanguageEnum["BmMl"] = "bm-ml";
    PageLanguageEnum["Bn"] = "bn";
    PageLanguageEnum["BnBd"] = "bn-bd";
    PageLanguageEnum["BnIn"] = "bn-in";
    PageLanguageEnum["Bo"] = "bo";
    PageLanguageEnum["BoCn"] = "bo-cn";
    PageLanguageEnum["BoIn"] = "bo-in";
    PageLanguageEnum["Br"] = "br";
    PageLanguageEnum["BrFr"] = "br-fr";
    PageLanguageEnum["Brx"] = "brx";
    PageLanguageEnum["BrxIn"] = "brx-in";
    PageLanguageEnum["Bs"] = "bs";
    PageLanguageEnum["BsBa"] = "bs-ba";
    PageLanguageEnum["Ca"] = "ca";
    PageLanguageEnum["CaAd"] = "ca-ad";
    PageLanguageEnum["CaEs"] = "ca-es";
    PageLanguageEnum["CaFr"] = "ca-fr";
    PageLanguageEnum["CaIt"] = "ca-it";
    PageLanguageEnum["Ccp"] = "ccp";
    PageLanguageEnum["CcpBd"] = "ccp-bd";
    PageLanguageEnum["CcpIn"] = "ccp-in";
    PageLanguageEnum["Ce"] = "ce";
    PageLanguageEnum["CeRu"] = "ce-ru";
    PageLanguageEnum["Ceb"] = "ceb";
    PageLanguageEnum["CebPh"] = "ceb-ph";
    PageLanguageEnum["Cgg"] = "cgg";
    PageLanguageEnum["CggUg"] = "cgg-ug";
    PageLanguageEnum["Chr"] = "chr";
    PageLanguageEnum["ChrUs"] = "chr-us";
    PageLanguageEnum["Ckb"] = "ckb";
    PageLanguageEnum["CkbIq"] = "ckb-iq";
    PageLanguageEnum["CkbIr"] = "ckb-ir";
    PageLanguageEnum["Cs"] = "cs";
    PageLanguageEnum["CsCz"] = "cs-cz";
    PageLanguageEnum["Cu"] = "cu";
    PageLanguageEnum["CuRu"] = "cu-ru";
    PageLanguageEnum["Cy"] = "cy";
    PageLanguageEnum["CyGb"] = "cy-gb";
    PageLanguageEnum["Da"] = "da";
    PageLanguageEnum["DaDk"] = "da-dk";
    PageLanguageEnum["DaGl"] = "da-gl";
    PageLanguageEnum["Dav"] = "dav";
    PageLanguageEnum["DavKe"] = "dav-ke";
    PageLanguageEnum["De"] = "de";
    PageLanguageEnum["DeAt"] = "de-at";
    PageLanguageEnum["DeBe"] = "de-be";
    PageLanguageEnum["DeCh"] = "de-ch";
    PageLanguageEnum["DeDe"] = "de-de";
    PageLanguageEnum["DeGr"] = "de-gr";
    PageLanguageEnum["DeIt"] = "de-it";
    PageLanguageEnum["DeLi"] = "de-li";
    PageLanguageEnum["DeLu"] = "de-lu";
    PageLanguageEnum["Dje"] = "dje";
    PageLanguageEnum["DjeNe"] = "dje-ne";
    PageLanguageEnum["Doi"] = "doi";
    PageLanguageEnum["DoiIn"] = "doi-in";
    PageLanguageEnum["Dsb"] = "dsb";
    PageLanguageEnum["DsbDe"] = "dsb-de";
    PageLanguageEnum["Dua"] = "dua";
    PageLanguageEnum["DuaCm"] = "dua-cm";
    PageLanguageEnum["Dyo"] = "dyo";
    PageLanguageEnum["DyoSn"] = "dyo-sn";
    PageLanguageEnum["Dz"] = "dz";
    PageLanguageEnum["DzBt"] = "dz-bt";
    PageLanguageEnum["Ebu"] = "ebu";
    PageLanguageEnum["EbuKe"] = "ebu-ke";
    PageLanguageEnum["Ee"] = "ee";
    PageLanguageEnum["EeGh"] = "ee-gh";
    PageLanguageEnum["EeTg"] = "ee-tg";
    PageLanguageEnum["El"] = "el";
    PageLanguageEnum["ElCy"] = "el-cy";
    PageLanguageEnum["ElGr"] = "el-gr";
    PageLanguageEnum["En"] = "en";
    PageLanguageEnum["En001"] = "en-001";
    PageLanguageEnum["En150"] = "en-150";
    PageLanguageEnum["EnAe"] = "en-ae";
    PageLanguageEnum["EnAg"] = "en-ag";
    PageLanguageEnum["EnAi"] = "en-ai";
    PageLanguageEnum["EnAs"] = "en-as";
    PageLanguageEnum["EnAt"] = "en-at";
    PageLanguageEnum["EnAu"] = "en-au";
    PageLanguageEnum["EnBb"] = "en-bb";
    PageLanguageEnum["EnBe"] = "en-be";
    PageLanguageEnum["EnBi"] = "en-bi";
    PageLanguageEnum["EnBm"] = "en-bm";
    PageLanguageEnum["EnBs"] = "en-bs";
    PageLanguageEnum["EnBw"] = "en-bw";
    PageLanguageEnum["EnBz"] = "en-bz";
    PageLanguageEnum["EnCa"] = "en-ca";
    PageLanguageEnum["EnCc"] = "en-cc";
    PageLanguageEnum["EnCh"] = "en-ch";
    PageLanguageEnum["EnCk"] = "en-ck";
    PageLanguageEnum["EnCm"] = "en-cm";
    PageLanguageEnum["EnCn"] = "en-cn";
    PageLanguageEnum["EnCx"] = "en-cx";
    PageLanguageEnum["EnCy"] = "en-cy";
    PageLanguageEnum["EnDe"] = "en-de";
    PageLanguageEnum["EnDg"] = "en-dg";
    PageLanguageEnum["EnDk"] = "en-dk";
    PageLanguageEnum["EnDm"] = "en-dm";
    PageLanguageEnum["EnEr"] = "en-er";
    PageLanguageEnum["EnFi"] = "en-fi";
    PageLanguageEnum["EnFj"] = "en-fj";
    PageLanguageEnum["EnFk"] = "en-fk";
    PageLanguageEnum["EnFm"] = "en-fm";
    PageLanguageEnum["EnGb"] = "en-gb";
    PageLanguageEnum["EnGd"] = "en-gd";
    PageLanguageEnum["EnGg"] = "en-gg";
    PageLanguageEnum["EnGh"] = "en-gh";
    PageLanguageEnum["EnGi"] = "en-gi";
    PageLanguageEnum["EnGm"] = "en-gm";
    PageLanguageEnum["EnGu"] = "en-gu";
    PageLanguageEnum["EnGy"] = "en-gy";
    PageLanguageEnum["EnHk"] = "en-hk";
    PageLanguageEnum["EnIe"] = "en-ie";
    PageLanguageEnum["EnIl"] = "en-il";
    PageLanguageEnum["EnIm"] = "en-im";
    PageLanguageEnum["EnIn"] = "en-in";
    PageLanguageEnum["EnIo"] = "en-io";
    PageLanguageEnum["EnJe"] = "en-je";
    PageLanguageEnum["EnJm"] = "en-jm";
    PageLanguageEnum["EnKe"] = "en-ke";
    PageLanguageEnum["EnKi"] = "en-ki";
    PageLanguageEnum["EnKn"] = "en-kn";
    PageLanguageEnum["EnKy"] = "en-ky";
    PageLanguageEnum["EnLc"] = "en-lc";
    PageLanguageEnum["EnLr"] = "en-lr";
    PageLanguageEnum["EnLs"] = "en-ls";
    PageLanguageEnum["EnLu"] = "en-lu";
    PageLanguageEnum["EnMg"] = "en-mg";
    PageLanguageEnum["EnMh"] = "en-mh";
    PageLanguageEnum["EnMo"] = "en-mo";
    PageLanguageEnum["EnMp"] = "en-mp";
    PageLanguageEnum["EnMs"] = "en-ms";
    PageLanguageEnum["EnMt"] = "en-mt";
    PageLanguageEnum["EnMu"] = "en-mu";
    PageLanguageEnum["EnMw"] = "en-mw";
    PageLanguageEnum["EnMx"] = "en-mx";
    PageLanguageEnum["EnMy"] = "en-my";
    PageLanguageEnum["EnNa"] = "en-na";
    PageLanguageEnum["EnNf"] = "en-nf";
    PageLanguageEnum["EnNg"] = "en-ng";
    PageLanguageEnum["EnNl"] = "en-nl";
    PageLanguageEnum["EnNr"] = "en-nr";
    PageLanguageEnum["EnNu"] = "en-nu";
    PageLanguageEnum["EnNz"] = "en-nz";
    PageLanguageEnum["EnPg"] = "en-pg";
    PageLanguageEnum["EnPh"] = "en-ph";
    PageLanguageEnum["EnPk"] = "en-pk";
    PageLanguageEnum["EnPn"] = "en-pn";
    PageLanguageEnum["EnPr"] = "en-pr";
    PageLanguageEnum["EnPw"] = "en-pw";
    PageLanguageEnum["EnRw"] = "en-rw";
    PageLanguageEnum["EnSb"] = "en-sb";
    PageLanguageEnum["EnSc"] = "en-sc";
    PageLanguageEnum["EnSd"] = "en-sd";
    PageLanguageEnum["EnSe"] = "en-se";
    PageLanguageEnum["EnSg"] = "en-sg";
    PageLanguageEnum["EnSh"] = "en-sh";
    PageLanguageEnum["EnSi"] = "en-si";
    PageLanguageEnum["EnSl"] = "en-sl";
    PageLanguageEnum["EnSs"] = "en-ss";
    PageLanguageEnum["EnSx"] = "en-sx";
    PageLanguageEnum["EnSz"] = "en-sz";
    PageLanguageEnum["EnTc"] = "en-tc";
    PageLanguageEnum["EnTk"] = "en-tk";
    PageLanguageEnum["EnTo"] = "en-to";
    PageLanguageEnum["EnTt"] = "en-tt";
    PageLanguageEnum["EnTv"] = "en-tv";
    PageLanguageEnum["EnTz"] = "en-tz";
    PageLanguageEnum["EnUg"] = "en-ug";
    PageLanguageEnum["EnUm"] = "en-um";
    PageLanguageEnum["EnUs"] = "en-us";
    PageLanguageEnum["EnVc"] = "en-vc";
    PageLanguageEnum["EnVg"] = "en-vg";
    PageLanguageEnum["EnVi"] = "en-vi";
    PageLanguageEnum["EnVu"] = "en-vu";
    PageLanguageEnum["EnWs"] = "en-ws";
    PageLanguageEnum["EnZa"] = "en-za";
    PageLanguageEnum["EnZm"] = "en-zm";
    PageLanguageEnum["EnZw"] = "en-zw";
    PageLanguageEnum["Eo"] = "eo";
    PageLanguageEnum["Eo001"] = "eo-001";
    PageLanguageEnum["Es"] = "es";
    PageLanguageEnum["Es419"] = "es-419";
    PageLanguageEnum["EsAr"] = "es-ar";
    PageLanguageEnum["EsBo"] = "es-bo";
    PageLanguageEnum["EsBr"] = "es-br";
    PageLanguageEnum["EsBz"] = "es-bz";
    PageLanguageEnum["EsCl"] = "es-cl";
    PageLanguageEnum["EsCo"] = "es-co";
    PageLanguageEnum["EsCr"] = "es-cr";
    PageLanguageEnum["EsCu"] = "es-cu";
    PageLanguageEnum["EsDo"] = "es-do";
    PageLanguageEnum["EsEa"] = "es-ea";
    PageLanguageEnum["EsEc"] = "es-ec";
    PageLanguageEnum["EsEs"] = "es-es";
    PageLanguageEnum["EsGq"] = "es-gq";
    PageLanguageEnum["EsGt"] = "es-gt";
    PageLanguageEnum["EsHn"] = "es-hn";
    PageLanguageEnum["EsIc"] = "es-ic";
    PageLanguageEnum["EsMx"] = "es-mx";
    PageLanguageEnum["EsNi"] = "es-ni";
    PageLanguageEnum["EsPa"] = "es-pa";
    PageLanguageEnum["EsPe"] = "es-pe";
    PageLanguageEnum["EsPh"] = "es-ph";
    PageLanguageEnum["EsPr"] = "es-pr";
    PageLanguageEnum["EsPy"] = "es-py";
    PageLanguageEnum["EsSv"] = "es-sv";
    PageLanguageEnum["EsUs"] = "es-us";
    PageLanguageEnum["EsUy"] = "es-uy";
    PageLanguageEnum["EsVe"] = "es-ve";
    PageLanguageEnum["Et"] = "et";
    PageLanguageEnum["EtEe"] = "et-ee";
    PageLanguageEnum["Eu"] = "eu";
    PageLanguageEnum["EuEs"] = "eu-es";
    PageLanguageEnum["Ewo"] = "ewo";
    PageLanguageEnum["EwoCm"] = "ewo-cm";
    PageLanguageEnum["Fa"] = "fa";
    PageLanguageEnum["FaAf"] = "fa-af";
    PageLanguageEnum["FaIr"] = "fa-ir";
    PageLanguageEnum["Ff"] = "ff";
    PageLanguageEnum["FfBf"] = "ff-bf";
    PageLanguageEnum["FfCm"] = "ff-cm";
    PageLanguageEnum["FfGh"] = "ff-gh";
    PageLanguageEnum["FfGm"] = "ff-gm";
    PageLanguageEnum["FfGn"] = "ff-gn";
    PageLanguageEnum["FfGw"] = "ff-gw";
    PageLanguageEnum["FfLr"] = "ff-lr";
    PageLanguageEnum["FfMr"] = "ff-mr";
    PageLanguageEnum["FfNe"] = "ff-ne";
    PageLanguageEnum["FfNg"] = "ff-ng";
    PageLanguageEnum["FfSl"] = "ff-sl";
    PageLanguageEnum["FfSn"] = "ff-sn";
    PageLanguageEnum["Fi"] = "fi";
    PageLanguageEnum["FiFi"] = "fi-fi";
    PageLanguageEnum["Fil"] = "fil";
    PageLanguageEnum["FilPh"] = "fil-ph";
    PageLanguageEnum["Fo"] = "fo";
    PageLanguageEnum["FoDk"] = "fo-dk";
    PageLanguageEnum["FoFo"] = "fo-fo";
    PageLanguageEnum["Fr"] = "fr";
    PageLanguageEnum["FrBe"] = "fr-be";
    PageLanguageEnum["FrBf"] = "fr-bf";
    PageLanguageEnum["FrBi"] = "fr-bi";
    PageLanguageEnum["FrBj"] = "fr-bj";
    PageLanguageEnum["FrBl"] = "fr-bl";
    PageLanguageEnum["FrCa"] = "fr-ca";
    PageLanguageEnum["FrCd"] = "fr-cd";
    PageLanguageEnum["FrCf"] = "fr-cf";
    PageLanguageEnum["FrCg"] = "fr-cg";
    PageLanguageEnum["FrCh"] = "fr-ch";
    PageLanguageEnum["FrCi"] = "fr-ci";
    PageLanguageEnum["FrCm"] = "fr-cm";
    PageLanguageEnum["FrDj"] = "fr-dj";
    PageLanguageEnum["FrDz"] = "fr-dz";
    PageLanguageEnum["FrFr"] = "fr-fr";
    PageLanguageEnum["FrGa"] = "fr-ga";
    PageLanguageEnum["FrGf"] = "fr-gf";
    PageLanguageEnum["FrGn"] = "fr-gn";
    PageLanguageEnum["FrGp"] = "fr-gp";
    PageLanguageEnum["FrGq"] = "fr-gq";
    PageLanguageEnum["FrHt"] = "fr-ht";
    PageLanguageEnum["FrKm"] = "fr-km";
    PageLanguageEnum["FrLu"] = "fr-lu";
    PageLanguageEnum["FrMa"] = "fr-ma";
    PageLanguageEnum["FrMc"] = "fr-mc";
    PageLanguageEnum["FrMf"] = "fr-mf";
    PageLanguageEnum["FrMg"] = "fr-mg";
    PageLanguageEnum["FrMl"] = "fr-ml";
    PageLanguageEnum["FrMq"] = "fr-mq";
    PageLanguageEnum["FrMr"] = "fr-mr";
    PageLanguageEnum["FrMu"] = "fr-mu";
    PageLanguageEnum["FrNc"] = "fr-nc";
    PageLanguageEnum["FrNe"] = "fr-ne";
    PageLanguageEnum["FrPf"] = "fr-pf";
    PageLanguageEnum["FrPm"] = "fr-pm";
    PageLanguageEnum["FrRe"] = "fr-re";
    PageLanguageEnum["FrRw"] = "fr-rw";
    PageLanguageEnum["FrSc"] = "fr-sc";
    PageLanguageEnum["FrSn"] = "fr-sn";
    PageLanguageEnum["FrSy"] = "fr-sy";
    PageLanguageEnum["FrTd"] = "fr-td";
    PageLanguageEnum["FrTg"] = "fr-tg";
    PageLanguageEnum["FrTn"] = "fr-tn";
    PageLanguageEnum["FrVu"] = "fr-vu";
    PageLanguageEnum["FrWf"] = "fr-wf";
    PageLanguageEnum["FrYt"] = "fr-yt";
    PageLanguageEnum["Fur"] = "fur";
    PageLanguageEnum["FurIt"] = "fur-it";
    PageLanguageEnum["Fy"] = "fy";
    PageLanguageEnum["FyNl"] = "fy-nl";
    PageLanguageEnum["Ga"] = "ga";
    PageLanguageEnum["GaGb"] = "ga-gb";
    PageLanguageEnum["GaIe"] = "ga-ie";
    PageLanguageEnum["Gd"] = "gd";
    PageLanguageEnum["GdGb"] = "gd-gb";
    PageLanguageEnum["Gl"] = "gl";
    PageLanguageEnum["GlEs"] = "gl-es";
    PageLanguageEnum["Gsw"] = "gsw";
    PageLanguageEnum["GswCh"] = "gsw-ch";
    PageLanguageEnum["GswFr"] = "gsw-fr";
    PageLanguageEnum["GswLi"] = "gsw-li";
    PageLanguageEnum["Gu"] = "gu";
    PageLanguageEnum["GuIn"] = "gu-in";
    PageLanguageEnum["Guz"] = "guz";
    PageLanguageEnum["GuzKe"] = "guz-ke";
    PageLanguageEnum["Gv"] = "gv";
    PageLanguageEnum["GvIm"] = "gv-im";
    PageLanguageEnum["Ha"] = "ha";
    PageLanguageEnum["HaGh"] = "ha-gh";
    PageLanguageEnum["HaNe"] = "ha-ne";
    PageLanguageEnum["HaNg"] = "ha-ng";
    PageLanguageEnum["Haw"] = "haw";
    PageLanguageEnum["HawUs"] = "haw-us";
    PageLanguageEnum["He"] = "he";
    PageLanguageEnum["Hi"] = "hi";
    PageLanguageEnum["HiIn"] = "hi-in";
    PageLanguageEnum["Hr"] = "hr";
    PageLanguageEnum["HrBa"] = "hr-ba";
    PageLanguageEnum["HrHr"] = "hr-hr";
    PageLanguageEnum["Hsb"] = "hsb";
    PageLanguageEnum["HsbDe"] = "hsb-de";
    PageLanguageEnum["Hu"] = "hu";
    PageLanguageEnum["HuHu"] = "hu-hu";
    PageLanguageEnum["Hy"] = "hy";
    PageLanguageEnum["HyAm"] = "hy-am";
    PageLanguageEnum["Ia"] = "ia";
    PageLanguageEnum["Ia001"] = "ia-001";
    PageLanguageEnum["Id"] = "id";
    PageLanguageEnum["Ig"] = "ig";
    PageLanguageEnum["IgNg"] = "ig-ng";
    PageLanguageEnum["Ii"] = "ii";
    PageLanguageEnum["IiCn"] = "ii-cn";
    PageLanguageEnum["IdId"] = "id-id";
    PageLanguageEnum["Is"] = "is";
    PageLanguageEnum["IsIs"] = "is-is";
    PageLanguageEnum["It"] = "it";
    PageLanguageEnum["ItCh"] = "it-ch";
    PageLanguageEnum["ItIt"] = "it-it";
    PageLanguageEnum["ItSm"] = "it-sm";
    PageLanguageEnum["ItVa"] = "it-va";
    PageLanguageEnum["HeIl"] = "he-il";
    PageLanguageEnum["Ja"] = "ja";
    PageLanguageEnum["JaJp"] = "ja-jp";
    PageLanguageEnum["Jgo"] = "jgo";
    PageLanguageEnum["JgoCm"] = "jgo-cm";
    PageLanguageEnum["Yi"] = "yi";
    PageLanguageEnum["Yi001"] = "yi-001";
    PageLanguageEnum["Jmc"] = "jmc";
    PageLanguageEnum["JmcTz"] = "jmc-tz";
    PageLanguageEnum["Jv"] = "jv";
    PageLanguageEnum["JvId"] = "jv-id";
    PageLanguageEnum["Ka"] = "ka";
    PageLanguageEnum["KaGe"] = "ka-ge";
    PageLanguageEnum["Kab"] = "kab";
    PageLanguageEnum["KabDz"] = "kab-dz";
    PageLanguageEnum["Kam"] = "kam";
    PageLanguageEnum["KamKe"] = "kam-ke";
    PageLanguageEnum["Kde"] = "kde";
    PageLanguageEnum["KdeTz"] = "kde-tz";
    PageLanguageEnum["Kea"] = "kea";
    PageLanguageEnum["KeaCv"] = "kea-cv";
    PageLanguageEnum["Khq"] = "khq";
    PageLanguageEnum["KhqMl"] = "khq-ml";
    PageLanguageEnum["Ki"] = "ki";
    PageLanguageEnum["KiKe"] = "ki-ke";
    PageLanguageEnum["Kk"] = "kk";
    PageLanguageEnum["KkKz"] = "kk-kz";
    PageLanguageEnum["Kkj"] = "kkj";
    PageLanguageEnum["KkjCm"] = "kkj-cm";
    PageLanguageEnum["Kl"] = "kl";
    PageLanguageEnum["KlGl"] = "kl-gl";
    PageLanguageEnum["Kln"] = "kln";
    PageLanguageEnum["KlnKe"] = "kln-ke";
    PageLanguageEnum["Km"] = "km";
    PageLanguageEnum["KmKh"] = "km-kh";
    PageLanguageEnum["Kn"] = "kn";
    PageLanguageEnum["KnIn"] = "kn-in";
    PageLanguageEnum["Ko"] = "ko";
    PageLanguageEnum["KoKp"] = "ko-kp";
    PageLanguageEnum["KoKr"] = "ko-kr";
    PageLanguageEnum["Kok"] = "kok";
    PageLanguageEnum["KokIn"] = "kok-in";
    PageLanguageEnum["Ks"] = "ks";
    PageLanguageEnum["KsIn"] = "ks-in";
    PageLanguageEnum["Ksb"] = "ksb";
    PageLanguageEnum["KsbTz"] = "ksb-tz";
    PageLanguageEnum["Ksf"] = "ksf";
    PageLanguageEnum["KsfCm"] = "ksf-cm";
    PageLanguageEnum["Ksh"] = "ksh";
    PageLanguageEnum["KshDe"] = "ksh-de";
    PageLanguageEnum["Kw"] = "kw";
    PageLanguageEnum["KwGb"] = "kw-gb";
    PageLanguageEnum["Ku"] = "ku";
    PageLanguageEnum["KuTr"] = "ku-tr";
    PageLanguageEnum["Ky"] = "ky";
    PageLanguageEnum["KyKg"] = "ky-kg";
    PageLanguageEnum["Lag"] = "lag";
    PageLanguageEnum["LagTz"] = "lag-tz";
    PageLanguageEnum["Lb"] = "lb";
    PageLanguageEnum["LbLu"] = "lb-lu";
    PageLanguageEnum["Lg"] = "lg";
    PageLanguageEnum["LgUg"] = "lg-ug";
    PageLanguageEnum["Lkt"] = "lkt";
    PageLanguageEnum["LktUs"] = "lkt-us";
    PageLanguageEnum["Ln"] = "ln";
    PageLanguageEnum["LnAo"] = "ln-ao";
    PageLanguageEnum["LnCd"] = "ln-cd";
    PageLanguageEnum["LnCf"] = "ln-cf";
    PageLanguageEnum["LnCg"] = "ln-cg";
    PageLanguageEnum["Lo"] = "lo";
    PageLanguageEnum["LoLa"] = "lo-la";
    PageLanguageEnum["Lrc"] = "lrc";
    PageLanguageEnum["LrcIq"] = "lrc-iq";
    PageLanguageEnum["LrcIr"] = "lrc-ir";
    PageLanguageEnum["Lt"] = "lt";
    PageLanguageEnum["LtLt"] = "lt-lt";
    PageLanguageEnum["Lu"] = "lu";
    PageLanguageEnum["LuCd"] = "lu-cd";
    PageLanguageEnum["Luo"] = "luo";
    PageLanguageEnum["LuoKe"] = "luo-ke";
    PageLanguageEnum["Luy"] = "luy";
    PageLanguageEnum["LuyKe"] = "luy-ke";
    PageLanguageEnum["Lv"] = "lv";
    PageLanguageEnum["LvLv"] = "lv-lv";
    PageLanguageEnum["Mai"] = "mai";
    PageLanguageEnum["MaiIn"] = "mai-in";
    PageLanguageEnum["Mas"] = "mas";
    PageLanguageEnum["MasKe"] = "mas-ke";
    PageLanguageEnum["MasTz"] = "mas-tz";
    PageLanguageEnum["Mer"] = "mer";
    PageLanguageEnum["MerKe"] = "mer-ke";
    PageLanguageEnum["Mfe"] = "mfe";
    PageLanguageEnum["MfeMu"] = "mfe-mu";
    PageLanguageEnum["Mg"] = "mg";
    PageLanguageEnum["MgMg"] = "mg-mg";
    PageLanguageEnum["Mgh"] = "mgh";
    PageLanguageEnum["MghMz"] = "mgh-mz";
    PageLanguageEnum["Mgo"] = "mgo";
    PageLanguageEnum["MgoCm"] = "mgo-cm";
    PageLanguageEnum["Mi"] = "mi";
    PageLanguageEnum["MiNz"] = "mi-nz";
    PageLanguageEnum["Mk"] = "mk";
    PageLanguageEnum["MkMk"] = "mk-mk";
    PageLanguageEnum["Ml"] = "ml";
    PageLanguageEnum["MlIn"] = "ml-in";
    PageLanguageEnum["Mn"] = "mn";
    PageLanguageEnum["MnMn"] = "mn-mn";
    PageLanguageEnum["Mni"] = "mni";
    PageLanguageEnum["MniIn"] = "mni-in";
    PageLanguageEnum["Mr"] = "mr";
    PageLanguageEnum["MrIn"] = "mr-in";
    PageLanguageEnum["Ms"] = "ms";
    PageLanguageEnum["MsBn"] = "ms-bn";
    PageLanguageEnum["MsId"] = "ms-id";
    PageLanguageEnum["MsMy"] = "ms-my";
    PageLanguageEnum["MsSg"] = "ms-sg";
    PageLanguageEnum["Mt"] = "mt";
    PageLanguageEnum["MtMt"] = "mt-mt";
    PageLanguageEnum["Mua"] = "mua";
    PageLanguageEnum["MuaCm"] = "mua-cm";
    PageLanguageEnum["My"] = "my";
    PageLanguageEnum["MyMm"] = "my-mm";
    PageLanguageEnum["Mzn"] = "mzn";
    PageLanguageEnum["MznIr"] = "mzn-ir";
    PageLanguageEnum["Naq"] = "naq";
    PageLanguageEnum["NaqNa"] = "naq-na";
    PageLanguageEnum["Nb"] = "nb";
    PageLanguageEnum["NbNo"] = "nb-no";
    PageLanguageEnum["NbSj"] = "nb-sj";
    PageLanguageEnum["Nd"] = "nd";
    PageLanguageEnum["NdZw"] = "nd-zw";
    PageLanguageEnum["Nds"] = "nds";
    PageLanguageEnum["NdsDe"] = "nds-de";
    PageLanguageEnum["NdsNl"] = "nds-nl";
    PageLanguageEnum["Ne"] = "ne";
    PageLanguageEnum["NeIn"] = "ne-in";
    PageLanguageEnum["NeNp"] = "ne-np";
    PageLanguageEnum["Nl"] = "nl";
    PageLanguageEnum["NlAw"] = "nl-aw";
    PageLanguageEnum["NlBe"] = "nl-be";
    PageLanguageEnum["NlCh"] = "nl-ch";
    PageLanguageEnum["NlBq"] = "nl-bq";
    PageLanguageEnum["NlCw"] = "nl-cw";
    PageLanguageEnum["NlLu"] = "nl-lu";
    PageLanguageEnum["NlNl"] = "nl-nl";
    PageLanguageEnum["NlSr"] = "nl-sr";
    PageLanguageEnum["NlSx"] = "nl-sx";
    PageLanguageEnum["Nmg"] = "nmg";
    PageLanguageEnum["NmgCm"] = "nmg-cm";
    PageLanguageEnum["Nn"] = "nn";
    PageLanguageEnum["NnNo"] = "nn-no";
    PageLanguageEnum["Nnh"] = "nnh";
    PageLanguageEnum["NnhCm"] = "nnh-cm";
    PageLanguageEnum["No"] = "no";
    PageLanguageEnum["NoNo"] = "no-no";
    PageLanguageEnum["Nus"] = "nus";
    PageLanguageEnum["NusSs"] = "nus-ss";
    PageLanguageEnum["Nyn"] = "nyn";
    PageLanguageEnum["NynUg"] = "nyn-ug";
    PageLanguageEnum["Om"] = "om";
    PageLanguageEnum["OmEt"] = "om-et";
    PageLanguageEnum["OmKe"] = "om-ke";
    PageLanguageEnum["Or"] = "or";
    PageLanguageEnum["OrIn"] = "or-in";
    PageLanguageEnum["Os"] = "os";
    PageLanguageEnum["OsGe"] = "os-ge";
    PageLanguageEnum["OsRu"] = "os-ru";
    PageLanguageEnum["Pa"] = "pa";
    PageLanguageEnum["PaIn"] = "pa-in";
    PageLanguageEnum["PaPk"] = "pa-pk";
    PageLanguageEnum["Pcm"] = "pcm";
    PageLanguageEnum["PcmNg"] = "pcm-ng";
    PageLanguageEnum["Pl"] = "pl";
    PageLanguageEnum["PlPl"] = "pl-pl";
    PageLanguageEnum["Prg"] = "prg";
    PageLanguageEnum["Prg001"] = "prg-001";
    PageLanguageEnum["Ps"] = "ps";
    PageLanguageEnum["PsAf"] = "ps-af";
    PageLanguageEnum["PsPk"] = "ps-pk";
    PageLanguageEnum["Pt"] = "pt";
    PageLanguageEnum["PtAo"] = "pt-ao";
    PageLanguageEnum["PtBr"] = "pt-br";
    PageLanguageEnum["PtCh"] = "pt-ch";
    PageLanguageEnum["PtCv"] = "pt-cv";
    PageLanguageEnum["PtGq"] = "pt-gq";
    PageLanguageEnum["PtGw"] = "pt-gw";
    PageLanguageEnum["PtLu"] = "pt-lu";
    PageLanguageEnum["PtMo"] = "pt-mo";
    PageLanguageEnum["PtMz"] = "pt-mz";
    PageLanguageEnum["PtPt"] = "pt-pt";
    PageLanguageEnum["PtSt"] = "pt-st";
    PageLanguageEnum["PtTl"] = "pt-tl";
    PageLanguageEnum["Qu"] = "qu";
    PageLanguageEnum["QuBo"] = "qu-bo";
    PageLanguageEnum["QuEc"] = "qu-ec";
    PageLanguageEnum["QuPe"] = "qu-pe";
    PageLanguageEnum["Rm"] = "rm";
    PageLanguageEnum["RmCh"] = "rm-ch";
    PageLanguageEnum["Rn"] = "rn";
    PageLanguageEnum["RnBi"] = "rn-bi";
    PageLanguageEnum["Ro"] = "ro";
    PageLanguageEnum["RoMd"] = "ro-md";
    PageLanguageEnum["RoRo"] = "ro-ro";
    PageLanguageEnum["Rof"] = "rof";
    PageLanguageEnum["RofTz"] = "rof-tz";
    PageLanguageEnum["Ru"] = "ru";
    PageLanguageEnum["RuBy"] = "ru-by";
    PageLanguageEnum["RuKg"] = "ru-kg";
    PageLanguageEnum["RuKz"] = "ru-kz";
    PageLanguageEnum["RuMd"] = "ru-md";
    PageLanguageEnum["RuRu"] = "ru-ru";
    PageLanguageEnum["RuUa"] = "ru-ua";
    PageLanguageEnum["Rw"] = "rw";
    PageLanguageEnum["RwRw"] = "rw-rw";
    PageLanguageEnum["Rwk"] = "rwk";
    PageLanguageEnum["RwkTz"] = "rwk-tz";
    PageLanguageEnum["Sa"] = "sa";
    PageLanguageEnum["SaIn"] = "sa-in";
    PageLanguageEnum["Sah"] = "sah";
    PageLanguageEnum["SahRu"] = "sah-ru";
    PageLanguageEnum["Saq"] = "saq";
    PageLanguageEnum["SaqKe"] = "saq-ke";
    PageLanguageEnum["Sat"] = "sat";
    PageLanguageEnum["SatIn"] = "sat-in";
    PageLanguageEnum["Sbp"] = "sbp";
    PageLanguageEnum["SbpTz"] = "sbp-tz";
    PageLanguageEnum["Sd"] = "sd";
    PageLanguageEnum["SdIn"] = "sd-in";
    PageLanguageEnum["SdPk"] = "sd-pk";
    PageLanguageEnum["Se"] = "se";
    PageLanguageEnum["SeFi"] = "se-fi";
    PageLanguageEnum["SeNo"] = "se-no";
    PageLanguageEnum["SeSe"] = "se-se";
    PageLanguageEnum["Seh"] = "seh";
    PageLanguageEnum["SehMz"] = "seh-mz";
    PageLanguageEnum["Ses"] = "ses";
    PageLanguageEnum["SesMl"] = "ses-ml";
    PageLanguageEnum["Sg"] = "sg";
    PageLanguageEnum["SgCf"] = "sg-cf";
    PageLanguageEnum["Shi"] = "shi";
    PageLanguageEnum["ShiMa"] = "shi-ma";
    PageLanguageEnum["Si"] = "si";
    PageLanguageEnum["SiLk"] = "si-lk";
    PageLanguageEnum["Sk"] = "sk";
    PageLanguageEnum["SkSk"] = "sk-sk";
    PageLanguageEnum["Sl"] = "sl";
    PageLanguageEnum["SlSi"] = "sl-si";
    PageLanguageEnum["Smn"] = "smn";
    PageLanguageEnum["SmnFi"] = "smn-fi";
    PageLanguageEnum["Sn"] = "sn";
    PageLanguageEnum["SnZw"] = "sn-zw";
    PageLanguageEnum["So"] = "so";
    PageLanguageEnum["SoDj"] = "so-dj";
    PageLanguageEnum["SoEt"] = "so-et";
    PageLanguageEnum["SoKe"] = "so-ke";
    PageLanguageEnum["SoSo"] = "so-so";
    PageLanguageEnum["Sq"] = "sq";
    PageLanguageEnum["SqAl"] = "sq-al";
    PageLanguageEnum["SqMk"] = "sq-mk";
    PageLanguageEnum["SqXk"] = "sq-xk";
    PageLanguageEnum["Sr"] = "sr";
    PageLanguageEnum["SrBa"] = "sr-ba";
    PageLanguageEnum["SrCs"] = "sr-cs";
    PageLanguageEnum["SrMe"] = "sr-me";
    PageLanguageEnum["SrRs"] = "sr-rs";
    PageLanguageEnum["SrXk"] = "sr-xk";
    PageLanguageEnum["Su"] = "su";
    PageLanguageEnum["SuId"] = "su-id";
    PageLanguageEnum["Sv"] = "sv";
    PageLanguageEnum["SvAx"] = "sv-ax";
    PageLanguageEnum["SvFi"] = "sv-fi";
    PageLanguageEnum["SvSe"] = "sv-se";
    PageLanguageEnum["Sw"] = "sw";
    PageLanguageEnum["SwCd"] = "sw-cd";
    PageLanguageEnum["SwKe"] = "sw-ke";
    PageLanguageEnum["SwTz"] = "sw-tz";
    PageLanguageEnum["SwUg"] = "sw-ug";
    PageLanguageEnum["Sy"] = "sy";
    PageLanguageEnum["Ta"] = "ta";
    PageLanguageEnum["TaIn"] = "ta-in";
    PageLanguageEnum["TaLk"] = "ta-lk";
    PageLanguageEnum["TaMy"] = "ta-my";
    PageLanguageEnum["TaSg"] = "ta-sg";
    PageLanguageEnum["Te"] = "te";
    PageLanguageEnum["TeIn"] = "te-in";
    PageLanguageEnum["Teo"] = "teo";
    PageLanguageEnum["TeoKe"] = "teo-ke";
    PageLanguageEnum["TeoUg"] = "teo-ug";
    PageLanguageEnum["Tg"] = "tg";
    PageLanguageEnum["TgTj"] = "tg-tj";
    PageLanguageEnum["Th"] = "th";
    PageLanguageEnum["ThTh"] = "th-th";
    PageLanguageEnum["Ti"] = "ti";
    PageLanguageEnum["TiEr"] = "ti-er";
    PageLanguageEnum["TiEt"] = "ti-et";
    PageLanguageEnum["Tk"] = "tk";
    PageLanguageEnum["TkTm"] = "tk-tm";
    PageLanguageEnum["Tl"] = "tl";
    PageLanguageEnum["To"] = "to";
    PageLanguageEnum["ToTo"] = "to-to";
    PageLanguageEnum["Tr"] = "tr";
    PageLanguageEnum["TrCy"] = "tr-cy";
    PageLanguageEnum["TrTr"] = "tr-tr";
    PageLanguageEnum["Tt"] = "tt";
    PageLanguageEnum["TtRu"] = "tt-ru";
    PageLanguageEnum["Twq"] = "twq";
    PageLanguageEnum["TwqNe"] = "twq-ne";
    PageLanguageEnum["Tzm"] = "tzm";
    PageLanguageEnum["TzmMa"] = "tzm-ma";
    PageLanguageEnum["Ug"] = "ug";
    PageLanguageEnum["UgCn"] = "ug-cn";
    PageLanguageEnum["Uk"] = "uk";
    PageLanguageEnum["UkUa"] = "uk-ua";
    PageLanguageEnum["Ur"] = "ur";
    PageLanguageEnum["UrIn"] = "ur-in";
    PageLanguageEnum["UrPk"] = "ur-pk";
    PageLanguageEnum["Uz"] = "uz";
    PageLanguageEnum["UzAf"] = "uz-af";
    PageLanguageEnum["UzUz"] = "uz-uz";
    PageLanguageEnum["Vai"] = "vai";
    PageLanguageEnum["VaiLr"] = "vai-lr";
    PageLanguageEnum["Vi"] = "vi";
    PageLanguageEnum["ViVn"] = "vi-vn";
    PageLanguageEnum["Vo"] = "vo";
    PageLanguageEnum["Vo001"] = "vo-001";
    PageLanguageEnum["Vun"] = "vun";
    PageLanguageEnum["VunTz"] = "vun-tz";
    PageLanguageEnum["Wae"] = "wae";
    PageLanguageEnum["WaeCh"] = "wae-ch";
    PageLanguageEnum["Wo"] = "wo";
    PageLanguageEnum["WoSn"] = "wo-sn";
    PageLanguageEnum["Xh"] = "xh";
    PageLanguageEnum["XhZa"] = "xh-za";
    PageLanguageEnum["Xog"] = "xog";
    PageLanguageEnum["XogUg"] = "xog-ug";
    PageLanguageEnum["Yav"] = "yav";
    PageLanguageEnum["YavCm"] = "yav-cm";
    PageLanguageEnum["Yo"] = "yo";
    PageLanguageEnum["YoBj"] = "yo-bj";
    PageLanguageEnum["YoNg"] = "yo-ng";
    PageLanguageEnum["Yue"] = "yue";
    PageLanguageEnum["YueCn"] = "yue-cn";
    PageLanguageEnum["YueHk"] = "yue-hk";
    PageLanguageEnum["Zgh"] = "zgh";
    PageLanguageEnum["ZghMa"] = "zgh-ma";
    PageLanguageEnum["Zh"] = "zh";
    PageLanguageEnum["ZhCn"] = "zh-cn";
    PageLanguageEnum["ZhHk"] = "zh-hk";
    PageLanguageEnum["ZhMo"] = "zh-mo";
    PageLanguageEnum["ZhSg"] = "zh-sg";
    PageLanguageEnum["ZhTw"] = "zh-tw";
    PageLanguageEnum["ZhHans"] = "zh-hans";
    PageLanguageEnum["ZhHant"] = "zh-hant";
    PageLanguageEnum["Zu"] = "zu";
    PageLanguageEnum["ZuZa"] = "zu-za";
})(PageLanguageEnum = exports.PageLanguageEnum || (exports.PageLanguageEnum = {}));
var PageContentTypeCategoryEnum;
(function (PageContentTypeCategoryEnum) {
    PageContentTypeCategoryEnum["_0"] = "0";
    PageContentTypeCategoryEnum["_1"] = "1";
    PageContentTypeCategoryEnum["_2"] = "2";
    PageContentTypeCategoryEnum["_3"] = "3";
    PageContentTypeCategoryEnum["_4"] = "4";
    PageContentTypeCategoryEnum["_5"] = "5";
    PageContentTypeCategoryEnum["_6"] = "6";
    PageContentTypeCategoryEnum["_7"] = "7";
    PageContentTypeCategoryEnum["_8"] = "8";
    PageContentTypeCategoryEnum["_9"] = "9";
    PageContentTypeCategoryEnum["_10"] = "10";
    PageContentTypeCategoryEnum["_11"] = "11";
    PageContentTypeCategoryEnum["_12"] = "12";
})(PageContentTypeCategoryEnum = exports.PageContentTypeCategoryEnum || (exports.PageContentTypeCategoryEnum = {}));
var PageAbStatusEnum;
(function (PageAbStatusEnum) {
    PageAbStatusEnum["Master"] = "master";
    PageAbStatusEnum["Variant"] = "variant";
    PageAbStatusEnum["LoserVariant"] = "loser_variant";
    PageAbStatusEnum["MabMaster"] = "mab_master";
    PageAbStatusEnum["MabVariant"] = "mab_variant";
    PageAbStatusEnum["AutomatedMaster"] = "automated_master";
    PageAbStatusEnum["AutomatedVariant"] = "automated_variant";
    PageAbStatusEnum["AutomatedLoserVariant"] = "automated_loser_variant";
})(PageAbStatusEnum = exports.PageAbStatusEnum || (exports.PageAbStatusEnum = {}));
var PageCurrentStateEnum;
(function (PageCurrentStateEnum) {
    PageCurrentStateEnum["Automated"] = "AUTOMATED";
    PageCurrentStateEnum["AutomatedDraft"] = "AUTOMATED_DRAFT";
    PageCurrentStateEnum["AutomatedSending"] = "AUTOMATED_SENDING";
    PageCurrentStateEnum["AutomatedForForm"] = "AUTOMATED_FOR_FORM";
    PageCurrentStateEnum["AutomatedForFormBuffer"] = "AUTOMATED_FOR_FORM_BUFFER";
    PageCurrentStateEnum["AutomatedForFormDraft"] = "AUTOMATED_FOR_FORM_DRAFT";
    PageCurrentStateEnum["AutomatedForFormLegacy"] = "AUTOMATED_FOR_FORM_LEGACY";
    PageCurrentStateEnum["BlogEmailDraft"] = "BLOG_EMAIL_DRAFT";
    PageCurrentStateEnum["BlogEmailPublished"] = "BLOG_EMAIL_PUBLISHED";
    PageCurrentStateEnum["Draft"] = "DRAFT";
    PageCurrentStateEnum["DraftAb"] = "DRAFT_AB";
    PageCurrentStateEnum["DraftAbVariant"] = "DRAFT_AB_VARIANT";
    PageCurrentStateEnum["Error"] = "ERROR";
    PageCurrentStateEnum["LoserAbVariant"] = "LOSER_AB_VARIANT";
    PageCurrentStateEnum["PageStub"] = "PAGE_STUB";
    PageCurrentStateEnum["PreProcessing"] = "PRE_PROCESSING";
    PageCurrentStateEnum["Processing"] = "PROCESSING";
    PageCurrentStateEnum["Published"] = "PUBLISHED";
    PageCurrentStateEnum["PublishedAb"] = "PUBLISHED_AB";
    PageCurrentStateEnum["PublishedAbVariant"] = "PUBLISHED_AB_VARIANT";
    PageCurrentStateEnum["PublishedOrScheduled"] = "PUBLISHED_OR_SCHEDULED";
    PageCurrentStateEnum["RssToEmailDraft"] = "RSS_TO_EMAIL_DRAFT";
    PageCurrentStateEnum["RssToEmailPublished"] = "RSS_TO_EMAIL_PUBLISHED";
    PageCurrentStateEnum["Scheduled"] = "SCHEDULED";
    PageCurrentStateEnum["ScheduledAb"] = "SCHEDULED_AB";
    PageCurrentStateEnum["ScheduledOrPublished"] = "SCHEDULED_OR_PUBLISHED";
    PageCurrentStateEnum["AutomatedAb"] = "AUTOMATED_AB";
    PageCurrentStateEnum["AutomatedAbVariant"] = "AUTOMATED_AB_VARIANT";
    PageCurrentStateEnum["AutomatedDraftAb"] = "AUTOMATED_DRAFT_AB";
    PageCurrentStateEnum["AutomatedDraftAbvariant"] = "AUTOMATED_DRAFT_ABVARIANT";
    PageCurrentStateEnum["AutomatedLoserAbvariant"] = "AUTOMATED_LOSER_ABVARIANT";
})(PageCurrentStateEnum = exports.PageCurrentStateEnum || (exports.PageCurrentStateEnum = {}));
//# sourceMappingURL=Page.js.map