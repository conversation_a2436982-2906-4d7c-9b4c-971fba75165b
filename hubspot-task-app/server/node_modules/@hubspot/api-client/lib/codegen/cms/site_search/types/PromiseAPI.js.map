{"version": 3, "file": "PromiseAPI.js", "sourceRoot": "", "sources": ["../../../../../codegen/cms/site_search/types/PromiseAPI.ts"], "names": [], "mappings": ";;;AAEA,8CAAyD;AAIzD,mDAAsD;AAGtD,MAAa,gBAAgB;IAGzB,YACI,aAA4B,EAC5B,cAAwC,EACxC,iBAA8C;QAE9C,IAAI,CAAC,GAAG,GAAG,IAAI,mCAAmB,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IACzF,CAAC;IAQM,mBAAmB,CAAC,SAAiB,EAAE,IAAwF,EAAE,QAAsC;;QAC1K,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAChF,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAQM,OAAO,CAAC,SAAiB,EAAE,IAAwF,EAAE,QAAsC;;QAC9J,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QACpE,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAuBM,kBAAkB,CAAC,CAAU,EAAE,KAAc,EAAE,MAAe,EAAE,QAAy6N,EAAE,WAAqB,EAAE,YAAsB,EAAE,eAAwB,EAAE,UAAmB,EAAE,WAAoB,EAAE,OAAgB,EAAE,UAAmB,EAAE,MAAsB,EAAE,IAA+F,EAAE,UAA0B,EAAE,QAAwB,EAAE,MAAyB,EAAE,OAAuB,EAAE,QAAsC;;QACl5O,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;QACzO,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAuBM,MAAM,CAAC,CAAU,EAAE,KAAc,EAAE,MAAe,EAAE,QAAy6N,EAAE,WAAqB,EAAE,YAAsB,EAAE,eAAwB,EAAE,UAAmB,EAAE,WAAoB,EAAE,OAAgB,EAAE,UAAmB,EAAE,MAAsB,EAAE,IAA+F,EAAE,UAA0B,EAAE,QAAwB,EAAE,MAAyB,EAAE,OAAuB,EAAE,QAAsC;;QACt4O,IAAI,iBAAmD,CAAA;QACvD,IAAI,QAAQ,EAAC;YAChB,iBAAiB,GAAG;gBACT,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,MAAA,QAAQ,CAAC,UAAU,0CAAE,GAAG,CAChC,CAAC,CAAC,EAAE,CAAC,IAAI,qCAAwB,CAAC,CAAC,CAAC,CACrD;gBACD,uBAAuB,EAAE,QAAQ,CAAC,uBAAuB;gBAC3C,WAAW,EAAE,QAAQ,CAAC,WAAW;aAC3C,CAAA;SACJ;QACM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;QAC7N,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;CAGJ;AAtID,4CAsIC"}