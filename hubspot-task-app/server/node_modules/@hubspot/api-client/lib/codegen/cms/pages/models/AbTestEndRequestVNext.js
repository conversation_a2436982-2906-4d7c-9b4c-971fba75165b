"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AbTestEndRequestVNext = void 0;
class AbTestEndRequestVNext {
    static getAttributeTypeMap() {
        return AbTestEndRequestVNext.attributeTypeMap;
    }
    constructor() {
    }
}
exports.AbTestEndRequestVNext = AbTestEndRequestVNext;
AbTestEndRequestVNext.discriminator = undefined;
AbTestEndRequestVNext.mapping = undefined;
AbTestEndRequestVNext.attributeTypeMap = [
    {
        "name": "winnerId",
        "baseName": "winnerId",
        "type": "string",
        "format": ""
    },
    {
        "name": "abTestId",
        "baseName": "abTestId",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=AbTestEndRequestVNext.js.map