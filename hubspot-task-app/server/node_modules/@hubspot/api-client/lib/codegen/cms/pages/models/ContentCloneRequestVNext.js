"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentCloneRequestVNext = void 0;
class ContentCloneRequestVNext {
    static getAttributeTypeMap() {
        return ContentCloneRequestVNext.attributeTypeMap;
    }
    constructor() {
    }
}
exports.ContentCloneRequestVNext = ContentCloneRequestVNext;
ContentCloneRequestVNext.discriminator = undefined;
ContentCloneRequestVNext.mapping = undefined;
ContentCloneRequestVNext.attributeTypeMap = [
    {
        "name": "cloneName",
        "baseName": "cloneName",
        "type": "string",
        "format": ""
    },
    {
        "name": "id",
        "baseName": "id",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=ContentCloneRequestVNext.js.map