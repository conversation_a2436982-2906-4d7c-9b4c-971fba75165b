"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentScheduleRequestVNext = void 0;
class ContentScheduleRequestVNext {
    static getAttributeTypeMap() {
        return ContentScheduleRequestVNext.attributeTypeMap;
    }
    constructor() {
    }
}
exports.ContentScheduleRequestVNext = ContentScheduleRequestVNext;
ContentScheduleRequestVNext.discriminator = undefined;
ContentScheduleRequestVNext.mapping = undefined;
ContentScheduleRequestVNext.attributeTypeMap = [
    {
        "name": "publishDate",
        "baseName": "publishDate",
        "type": "Date",
        "format": "date-time"
    },
    {
        "name": "id",
        "baseName": "id",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=ContentScheduleRequestVNext.js.map