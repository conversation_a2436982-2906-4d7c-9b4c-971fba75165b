"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AbTestRerunRequestVNext = void 0;
class AbTestRerunRequestVNext {
    static getAttributeTypeMap() {
        return AbTestRerunRequestVNext.attributeTypeMap;
    }
    constructor() {
    }
}
exports.AbTestRerunRequestVNext = AbTestRerunRequestVNext;
AbTestRerunRequestVNext.discriminator = undefined;
AbTestRerunRequestVNext.mapping = undefined;
AbTestRerunRequestVNext.attributeTypeMap = [
    {
        "name": "variationId",
        "baseName": "variationId",
        "type": "string",
        "format": ""
    },
    {
        "name": "abTestId",
        "baseName": "abTestId",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=AbTestRerunRequestVNext.js.map