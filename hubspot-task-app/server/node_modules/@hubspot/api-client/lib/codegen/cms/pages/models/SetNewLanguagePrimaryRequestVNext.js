"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SetNewLanguagePrimaryRequestVNext = void 0;
class SetNewLanguagePrimaryRequestVNext {
    static getAttributeTypeMap() {
        return SetNewLanguagePrimaryRequestVNext.attributeTypeMap;
    }
    constructor() {
    }
}
exports.SetNewLanguagePrimaryRequestVNext = SetNewLanguagePrimaryRequestVNext;
SetNewLanguagePrimaryRequestVNext.discriminator = undefined;
SetNewLanguagePrimaryRequestVNext.mapping = undefined;
SetNewLanguagePrimaryRequestVNext.attributeTypeMap = [
    {
        "name": "id",
        "baseName": "id",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=SetNewLanguagePrimaryRequestVNext.js.map