{"version": 3, "file": "ObjectParamAPI.js", "sourceRoot": "", "sources": ["../../../../../codegen/cms/source_code/types/ObjectParamAPI.ts"], "names": [], "mappings": ";;;AAQA,mDAAuD;AAqFvD,MAAa,gBAAgB;IAGzB,YAAmB,aAA4B,EAAE,cAAyC,EAAE,iBAA+C;QACvI,IAAI,CAAC,GAAG,GAAG,IAAI,oCAAoB,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC1F,CAAC;IAOM,mBAAmB,CAAC,KAA+B,EAAE,OAA8B;QACtF,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAC7F,CAAC;IAOM,OAAO,CAAC,KAA+B,EAAE,OAA8B;QAC1E,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACjF,CAAC;IAOM,kBAAkB,CAAC,KAA8B,EAAE,OAA8B;QACpF,OAAO,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACxG,CAAC;IAOM,MAAM,CAAC,KAA8B,EAAE,OAA8B;QACxE,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAC5F,CAAC;IAOM,0BAA0B,CAAC,KAAsC,EAAE,OAA8B;QACpG,OAAO,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAChH,CAAC;IAOM,cAAc,CAAC,KAAsC,EAAE,OAA8B;QACxF,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACpG,CAAC;IAOM,oBAAoB,CAAC,KAAgC,EAAE,OAA8B;QACxF,OAAO,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAC9F,CAAC;IAOM,QAAQ,CAAC,KAAgC,EAAE,OAA8B;QAC5E,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAClF,CAAC;CAEJ;AA/ED,4CA+EC;AAED,mDAAuD;AAsBvD,MAAa,gBAAgB;IAGzB,YAAmB,aAA4B,EAAE,cAAyC,EAAE,iBAA+C;QACvI,IAAI,CAAC,GAAG,GAAG,IAAI,oCAAoB,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC1F,CAAC;IAOM,mBAAmB,CAAC,KAA+B,EAAE,OAA8B;QACtF,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,CAAC,kBAAkB,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACxF,CAAC;IAOM,OAAO,CAAC,KAA+B,EAAE,OAA8B;QAC1E,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAC5E,CAAC;IAOM,0BAA0B,CAAC,KAAsC,EAAE,OAA8B;QACpG,OAAO,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,CAAC,MAAM,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACnF,CAAC;IAOM,cAAc,CAAC,KAAsC,EAAE,OAA8B;QACxF,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACvE,CAAC;CAEJ;AA3CD,4CA2CC;AAED,mDAAwD;AA2BxD,MAAa,iBAAiB;IAG1B,YAAmB,aAA4B,EAAE,cAA0C,EAAE,iBAAgD;QACzI,IAAI,CAAC,GAAG,GAAG,IAAI,qCAAqB,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC3F,CAAC;IAOM,eAAe,CAAC,KAA4B,EAAE,OAA8B;QAC/E,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,UAAU,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAC3G,CAAC;IAOM,GAAG,CAAC,KAA4B,EAAE,OAA8B;QACnE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,UAAU,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAC/F,CAAC;CAEJ;AAzBD,8CAyBC;AAED,mDAA0D;AAoB1D,MAAa,mBAAmB;IAG5B,YAAmB,aAA4B,EAAE,cAA4C,EAAE,iBAAkD;QAC7I,IAAI,CAAC,GAAG,GAAG,IAAI,uCAAuB,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC7F,CAAC;IAOM,sBAAsB,CAAC,KAAqC,EAAE,OAA8B;QAC/F,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACzF,CAAC;IAOM,UAAU,CAAC,KAAqC,EAAE,OAA8B;QACnF,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAC7E,CAAC;CAEJ;AAzBD,kDAyBC"}