"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicSearchResults = void 0;
class PublicSearchResults {
    static getAttributeTypeMap() {
        return PublicSearchResults.attributeTypeMap;
    }
    constructor() {
    }
}
exports.PublicSearchResults = PublicSearchResults;
PublicSearchResults.discriminator = undefined;
PublicSearchResults.mapping = undefined;
PublicSearchResults.attributeTypeMap = [
    {
        "name": "total",
        "baseName": "total",
        "type": "number",
        "format": "int32"
    },
    {
        "name": "searchTerm",
        "baseName": "searchTerm",
        "type": "string",
        "format": ""
    },
    {
        "name": "offset",
        "baseName": "offset",
        "type": "number",
        "format": "int32"
    },
    {
        "name": "limit",
        "baseName": "limit",
        "type": "number",
        "format": "int32"
    },
    {
        "name": "page",
        "baseName": "page",
        "type": "number",
        "format": "int32"
    },
    {
        "name": "results",
        "baseName": "results",
        "type": "Array<ContentSearchResult>",
        "format": ""
    }
];
//# sourceMappingURL=PublicSearchResults.js.map