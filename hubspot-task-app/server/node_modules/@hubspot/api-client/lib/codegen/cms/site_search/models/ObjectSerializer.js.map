{"version": 3, "file": "ObjectSerializer.js", "sourceRoot": "", "sources": ["../../../../../codegen/cms/site_search/models/ObjectSerializer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,gEAA8C;AAC9C,wDAAsC;AACtC,wDAAsC;AACtC,yDAAuC;AACvC,uDAAqC;AACrC,gEAA8C;AAE9C,uEAAqF;AACrF,uDAAoD;AACpD,uDAAwD;AACxD,yDAAsD;AACtD,qDAAkD;AAClD,uEAAoE;AAGpE,IAAI,UAAU,GAAG;IACG,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;IACT,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;CACP,CAAC;AAEnB,IAAI,QAAQ,GAAgB,IAAI,GAAG,CAAS;IACxC,iCAAiC;IACjC,6BAA6B;IAC7B,qBAAqB;CACxB,CAAC,CAAC;AAEH,IAAI,OAAO,GAA2B;IAClC,qBAAqB,EAAE,yCAAmB;IAC1C,aAAa,EAAE,yBAAW;IAC1B,aAAa,EAAE,yBAAW;IAC1B,cAAc,EAAE,2BAAY;IAC5B,YAAY,EAAE,uBAAU;IACxB,qBAAqB,EAAE,yCAAmB;CAC7C,CAAA;AAiBD,MAAM,aAAa,GAAG,CAAC,QAAgB,EAAsB,EAAE;IAC3D,MAAM,CAAC,IAAI,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtD,OAAO;QACH,IAAI;QACJ,OAAO;QACP,aAAa,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;KACpC,CAAC;AACN,CAAC,CAAC;AAKF,MAAM,wBAAwB,GAAG,CAAC,SAAsD,EAAqB,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;AAGjK,MAAM,8BAA8B,GAAG,CAAC,IAAY,EAAE,OAAgB,EAAqB,EAAE,CAAC,wBAAwB,CAAC,CAAC,UAAU,EAAE,EAAE;IAClI,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI;QAAE,OAAO,KAAK,CAAC;IAC3C,IAAI,OAAO,IAAI,IAAI,IAAI,UAAU,CAAC,OAAO,KAAK,OAAO;QAAE,OAAO,KAAK,CAAC;IACpE,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC,CAAC;AAGH,MAAM,kBAAkB,GAAG,8BAA8B,CAAC,MAAM,CAAC,CAAC;AAClE,MAAM,cAAc,GAAG,8BAA8B,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC7E,MAAM,kBAAkB,GAAG,wBAAwB,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,KAAK,aAAa,IAAI,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC;AACnK,MAAM,qBAAqB,GAAG,8BAA8B,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AAC5F,MAAM,wBAAwB,GAAG,8BAA8B,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;AAGxG,MAAM,uCAAuC,GAAwB;IACjE,cAAc;IACd,kBAAkB;IAClB,kBAAkB;IAClB,qBAAqB;IACrB,wBAAwB;CAC3B,CAAC;AAEF,MAAM,cAAc,GAAG,SAAS,CAAC;AACjC,MAAM,cAAc,GAAG,cAAc,CAAC;AACtC,MAAM,WAAW,GAAG,QAAQ,CAAC;AAC7B,MAAM,WAAW,GAAG,GAAG,CAAC;AACxB,MAAM,SAAS,GAAG,mBAAmB,CAAC;AACtC,MAAM,SAAS,GAAG,KAAK,CAAC;AAExB,MAAa,gBAAgB;IAClB,MAAM,CAAC,eAAe,CAAC,IAAS,EAAE,YAAoB;QACzD,IAAI,IAAI,IAAI,SAAS,EAAE;YACnB,OAAO,YAAY,CAAC;SACvB;aAAM,IAAI,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;YAC9D,OAAO,YAAY,CAAC;SACvB;aAAM,IAAI,YAAY,KAAK,MAAM,EAAE;YAChC,OAAO,YAAY,CAAC;SACvB;aAAM;YACH,IAAI,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;gBAC5B,OAAO,YAAY,CAAC;aACvB;YAED,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBACxB,OAAO,YAAY,CAAC;aACvB;YAGD,IAAI,qBAAqB,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC;YAChE,IAAI,qBAAqB,IAAI,IAAI,EAAE;gBAC/B,OAAO,YAAY,CAAC;aACvB;iBAAM;gBACH,IAAI,IAAI,CAAC,qBAAqB,CAAC,EAAE;oBAC7B,IAAI,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBACpD,IAAI,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC;oBAC5C,IAAI,OAAO,IAAI,SAAS,IAAI,OAAO,CAAC,iBAAiB,CAAC,EAAE;wBACpD,OAAO,OAAO,CAAC,iBAAiB,CAAC,CAAC;qBACrC;yBAAM,IAAG,OAAO,CAAC,iBAAiB,CAAC,EAAE;wBAClC,OAAO,iBAAiB,CAAC;qBAC5B;yBAAM;wBACH,OAAO,YAAY,CAAC;qBACvB;iBACJ;qBAAM;oBACH,OAAO,YAAY,CAAC;iBACvB;aACJ;SACJ;IACL,CAAC;IAEM,MAAM,CAAC,SAAS,CAAC,IAAS,EAAE,IAAY,EAAE,MAAc;QAC3D,IAAI,IAAI,IAAI,SAAS,EAAE;YACnB,OAAO,IAAI,CAAC;SACf;aAAM,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;YACtD,OAAO,IAAI,CAAC;SACf;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YACtC,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC5D,OAAO,gBAAgB,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SAC5D;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YACtC,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC5D,OAAO,gBAAgB,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SAC5D;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YACrC,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,eAAe,GAAU,EAAE,CAAC;YAChC,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;gBACnB,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;aAC3E;YACD,OAAO,eAAe,CAAC;SAC1B;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YACnC,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACtE,IAAI,eAAe,GAA2B,EAAE,CAAC;YACjD,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;gBAClB,eAAe,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,SAAS,CAC7C,IAAI,CAAC,GAAG,CAAC,EACT,OAAO,EACP,MAAM,CACT,CAAC;aACL;YACD,OAAO,eAAe,CAAC;SAC1B;aAAM,IAAI,IAAI,KAAK,MAAM,EAAE;YACxB,IAAI,MAAM,IAAI,MAAM,EAAE;gBAClB,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAC,CAAC,CAAA;gBAC7B,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAA;gBAC9D,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBACzB,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;gBAEvD,OAAO,IAAI,CAAC,WAAW,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;aACvD;iBAAM;gBACH,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;aAC7B;SACJ;aAAM;YACH,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACpB,OAAO,IAAI,CAAC;aACf;YACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAChB,OAAO,IAAI,CAAC;aACf;YAGD,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAGxC,IAAI,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,CAAC;YACzD,IAAI,QAAQ,GAA2B,EAAE,CAAC;YAC1C,KAAK,IAAI,aAAa,IAAI,cAAc,EAAE;gBACtC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;aACrI;YACD,OAAO,QAAQ,CAAC;SACnB;IACL,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,IAAS,EAAE,IAAY,EAAE,MAAc;QAE7D,IAAI,GAAG,gBAAgB,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACpD,IAAI,IAAI,IAAI,SAAS,EAAE;YACnB,OAAO,IAAI,CAAC;SACf;aAAM,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;YACtD,OAAO,IAAI,CAAC;SACf;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YACtC,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC5D,OAAO,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SAC9D;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YACtC,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC5D,OAAO,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SAC9D;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YACrC,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,eAAe,GAAU,EAAE,CAAC;YAChC,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;gBACnB,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;aAC7E;YACD,OAAO,eAAe,CAAC;SAC1B;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YACnC,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACtE,IAAI,eAAe,GAA2B,EAAE,CAAC;YACjD,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;gBAClB,eAAe,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,WAAW,CAC/C,IAAI,CAAC,GAAG,CAAC,EACT,OAAO,EACP,MAAM,CACT,CAAC;aACL;YACD,OAAO,eAAe,CAAC;SAC1B;aAAM,IAAI,IAAI,KAAK,MAAM,EAAE;YACxB,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;SACzB;aAAM;YACH,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACpB,OAAO,IAAI,CAAC;aACf;YAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAChB,OAAO,IAAI,CAAC;aACf;YACD,IAAI,QAAQ,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,IAAI,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,CAAC;YACzD,KAAK,IAAI,aAAa,IAAI,cAAc,EAAE;gBACtC,IAAI,KAAK,GAAG,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;gBACjH,IAAI,KAAK,KAAK,SAAS,EAAE;oBACrB,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;iBACxC;aACJ;YACD,OAAO,QAAQ,CAAC;SACnB;IACL,CAAC;IASM,MAAM,CAAC,kBAAkB,CAAC,SAA6B;;QAC1D,IAAI,SAAS,KAAK,SAAS,EAAE;YACzB,OAAO,SAAS,CAAC;SACpB;QACD,OAAO,CAAC,MAAA,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,mCAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAChE,CAAC;IAQM,MAAM,CAAC,qBAAqB,CAAC,UAAyB;QAEzD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,kBAAkB,CAAC;SAC7B;QAED,MAAM,gBAAgB,GAAG,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAE7E,KAAK,MAAM,SAAS,IAAI,uCAAuC,EAAE;YAC7D,KAAK,MAAM,SAAS,IAAI,gBAAgB,EAAE;gBACtC,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,SAAS,CAAC,EAAE;oBAC3C,OAAO,SAAS,CAAC;iBACpB;aACJ;SACJ;QAED,MAAM,IAAI,KAAK,CAAC,+CAA+C,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7F,CAAC;IAKM,MAAM,CAAC,SAAS,CAAC,IAAS,EAAE,SAAiB;QAChD,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE;YAC/B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;SACvB;QAED,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE;YAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SAC/B;QAED,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,SAAS,GAAG,kDAAkD,CAAC,CAAC;IACvG,CAAC;IAKM,MAAM,CAAC,KAAK,CAAC,OAAe,EAAE,SAA6B;QAC9D,IAAI,SAAS,KAAK,SAAS,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACrE;QAED,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE;YAC/B,OAAO,OAAO,CAAC;SAClB;QAED,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE;YAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAC9B;QAED,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,SAAS,GAAG,8CAA8C,CAAC,CAAC;IACnG,CAAC;CACJ;AAjOD,4CAiOC"}