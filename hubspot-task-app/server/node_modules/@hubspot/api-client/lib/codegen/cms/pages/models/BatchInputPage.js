"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchInputPage = void 0;
class BatchInputPage {
    static getAttributeTypeMap() {
        return BatchInputPage.attributeTypeMap;
    }
    constructor() {
    }
}
exports.BatchInputPage = BatchInputPage;
BatchInputPage.discriminator = undefined;
BatchInputPage.mapping = undefined;
BatchInputPage.attributeTypeMap = [
    {
        "name": "inputs",
        "baseName": "inputs",
        "type": "Array<Page>",
        "format": ""
    }
];
//# sourceMappingURL=BatchInputPage.js.map