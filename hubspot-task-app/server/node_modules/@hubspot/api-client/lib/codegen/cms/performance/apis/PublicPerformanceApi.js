"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicPerformanceApiResponseProcessor = exports.PublicPerformanceApiRequestFactory = void 0;
const baseapi_1 = require("./baseapi");
const http_1 = require("../http/http");
const ObjectSerializer_1 = require("../models/ObjectSerializer");
const exception_1 = require("./exception");
const util_1 = require("../util");
class PublicPerformanceApiRequestFactory extends baseapi_1.BaseAPIRequestFactory {
    getPage(domain, path, pad, sum, period, interval, start, end, _options) {
        var _a, _b, _c;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            const localVarPath = '/cms/v3/performance/';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (domain !== undefined) {
                requestContext.setQueryParam("domain", ObjectSerializer_1.ObjectSerializer.serialize(domain, "string", ""));
            }
            if (path !== undefined) {
                requestContext.setQueryParam("path", ObjectSerializer_1.ObjectSerializer.serialize(path, "string", ""));
            }
            if (pad !== undefined) {
                requestContext.setQueryParam("pad", ObjectSerializer_1.ObjectSerializer.serialize(pad, "boolean", ""));
            }
            if (sum !== undefined) {
                requestContext.setQueryParam("sum", ObjectSerializer_1.ObjectSerializer.serialize(sum, "boolean", ""));
            }
            if (period !== undefined) {
                requestContext.setQueryParam("period", ObjectSerializer_1.ObjectSerializer.serialize(period, "string", ""));
            }
            if (interval !== undefined) {
                requestContext.setQueryParam("interval", ObjectSerializer_1.ObjectSerializer.serialize(interval, "string", ""));
            }
            if (start !== undefined) {
                requestContext.setQueryParam("start", ObjectSerializer_1.ObjectSerializer.serialize(start, "number", "int64"));
            }
            if (end !== undefined) {
                requestContext.setQueryParam("end", ObjectSerializer_1.ObjectSerializer.serialize(end, "number", "int64"));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = ((_a = _options === null || _options === void 0 ? void 0 : _options.authMethods) === null || _a === void 0 ? void 0 : _a.default) || ((_c = (_b = this.configuration) === null || _b === void 0 ? void 0 : _b.authMethods) === null || _c === void 0 ? void 0 : _c.default);
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
    getUptime(domain, path, pad, sum, period, interval, start, end, _options) {
        var _a, _b, _c;
        return __awaiter(this, void 0, void 0, function* () {
            let _config = _options || this.configuration;
            const localVarPath = '/cms/v3/performance/uptime';
            const requestContext = _config.baseServer.makeRequestContext(localVarPath, http_1.HttpMethod.GET);
            requestContext.setHeaderParam("Accept", "application/json, */*;q=0.8");
            if (domain !== undefined) {
                requestContext.setQueryParam("domain", ObjectSerializer_1.ObjectSerializer.serialize(domain, "string", ""));
            }
            if (path !== undefined) {
                requestContext.setQueryParam("path", ObjectSerializer_1.ObjectSerializer.serialize(path, "string", ""));
            }
            if (pad !== undefined) {
                requestContext.setQueryParam("pad", ObjectSerializer_1.ObjectSerializer.serialize(pad, "boolean", ""));
            }
            if (sum !== undefined) {
                requestContext.setQueryParam("sum", ObjectSerializer_1.ObjectSerializer.serialize(sum, "boolean", ""));
            }
            if (period !== undefined) {
                requestContext.setQueryParam("period", ObjectSerializer_1.ObjectSerializer.serialize(period, "string", ""));
            }
            if (interval !== undefined) {
                requestContext.setQueryParam("interval", ObjectSerializer_1.ObjectSerializer.serialize(interval, "string", ""));
            }
            if (start !== undefined) {
                requestContext.setQueryParam("start", ObjectSerializer_1.ObjectSerializer.serialize(start, "number", "int64"));
            }
            if (end !== undefined) {
                requestContext.setQueryParam("end", ObjectSerializer_1.ObjectSerializer.serialize(end, "number", "int64"));
            }
            let authMethod;
            authMethod = _config.authMethods["oauth2"];
            if (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication) {
                yield (authMethod === null || authMethod === void 0 ? void 0 : authMethod.applySecurityAuthentication(requestContext));
            }
            const defaultAuth = ((_a = _options === null || _options === void 0 ? void 0 : _options.authMethods) === null || _a === void 0 ? void 0 : _a.default) || ((_c = (_b = this.configuration) === null || _b === void 0 ? void 0 : _b.authMethods) === null || _c === void 0 ? void 0 : _c.default);
            if (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication) {
                yield (defaultAuth === null || defaultAuth === void 0 ? void 0 : defaultAuth.applySecurityAuthentication(requestContext));
            }
            return requestContext;
        });
    }
}
exports.PublicPerformanceApiRequestFactory = PublicPerformanceApiRequestFactory;
class PublicPerformanceApiResponseProcessor {
    getPageWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "PublicPerformanceResponse", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "PublicPerformanceResponse", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
    getUptimeWithHttpInfo(response) {
        return __awaiter(this, void 0, void 0, function* () {
            const contentType = ObjectSerializer_1.ObjectSerializer.normalizeMediaType(response.headers["content-type"]);
            if ((0, util_1.isCodeInRange)("200", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "PublicPerformanceResponse", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            if ((0, util_1.isCodeInRange)("0", response.httpStatusCode)) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "Error", "");
                throw new exception_1.ApiException(response.httpStatusCode, "An error occurred.", body, response.headers);
            }
            if (response.httpStatusCode >= 200 && response.httpStatusCode <= 299) {
                const body = ObjectSerializer_1.ObjectSerializer.deserialize(ObjectSerializer_1.ObjectSerializer.parse(yield response.body.text(), contentType), "PublicPerformanceResponse", "");
                return new http_1.HttpInfo(response.httpStatusCode, response.headers, response.body, body);
            }
            throw new exception_1.ApiException(response.httpStatusCode, "Unknown API Status Code!", yield response.getBodyAsAny(), response.headers);
        });
    }
}
exports.PublicPerformanceApiResponseProcessor = PublicPerformanceApiResponseProcessor;
//# sourceMappingURL=PublicPerformanceApi.js.map