"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectSerializer = void 0;
__exportStar(require("../models/AbTestCreateRequestVNext"), exports);
__exportStar(require("../models/AbTestEndRequestVNext"), exports);
__exportStar(require("../models/AbTestRerunRequestVNext"), exports);
__exportStar(require("../models/Angle"), exports);
__exportStar(require("../models/AttachToLangPrimaryRequestVNext"), exports);
__exportStar(require("../models/BackgroundImage"), exports);
__exportStar(require("../models/BatchInputContentFolder"), exports);
__exportStar(require("../models/BatchInputJsonNode"), exports);
__exportStar(require("../models/BatchInputPage"), exports);
__exportStar(require("../models/BatchInputString"), exports);
__exportStar(require("../models/BatchResponseContentFolder"), exports);
__exportStar(require("../models/BatchResponseContentFolderWithErrors"), exports);
__exportStar(require("../models/BatchResponsePage"), exports);
__exportStar(require("../models/BatchResponsePageWithErrors"), exports);
__exportStar(require("../models/CollectionResponseWithTotalContentFolderForwardPaging"), exports);
__exportStar(require("../models/CollectionResponseWithTotalPageForwardPaging"), exports);
__exportStar(require("../models/CollectionResponseWithTotalVersionContentFolder"), exports);
__exportStar(require("../models/CollectionResponseWithTotalVersionPage"), exports);
__exportStar(require("../models/ColorStop"), exports);
__exportStar(require("../models/ContentCloneRequestVNext"), exports);
__exportStar(require("../models/ContentFolder"), exports);
__exportStar(require("../models/ContentLanguageCloneRequestVNext"), exports);
__exportStar(require("../models/ContentLanguageVariation"), exports);
__exportStar(require("../models/ContentScheduleRequestVNext"), exports);
__exportStar(require("../models/DetachFromLangGroupRequestVNext"), exports);
__exportStar(require("../models/ErrorDetail"), exports);
__exportStar(require("../models/ForwardPaging"), exports);
__exportStar(require("../models/Gradient"), exports);
__exportStar(require("../models/LayoutSection"), exports);
__exportStar(require("../models/ModelError"), exports);
__exportStar(require("../models/NextPage"), exports);
__exportStar(require("../models/Page"), exports);
__exportStar(require("../models/Paging"), exports);
__exportStar(require("../models/PreviousPage"), exports);
__exportStar(require("../models/RGBAColor"), exports);
__exportStar(require("../models/RowMetaData"), exports);
__exportStar(require("../models/SetNewLanguagePrimaryRequestVNext"), exports);
__exportStar(require("../models/SideOrCorner"), exports);
__exportStar(require("../models/StandardError"), exports);
__exportStar(require("../models/Styles"), exports);
__exportStar(require("../models/UpdateLanguagesRequestVNext"), exports);
__exportStar(require("../models/VersionContentFolder"), exports);
__exportStar(require("../models/VersionPage"), exports);
__exportStar(require("../models/VersionUser"), exports);
const AbTestCreateRequestVNext_1 = require("../models/AbTestCreateRequestVNext");
const AbTestEndRequestVNext_1 = require("../models/AbTestEndRequestVNext");
const AbTestRerunRequestVNext_1 = require("../models/AbTestRerunRequestVNext");
const Angle_1 = require("../models/Angle");
const AttachToLangPrimaryRequestVNext_1 = require("../models/AttachToLangPrimaryRequestVNext");
const BackgroundImage_1 = require("../models/BackgroundImage");
const BatchInputContentFolder_1 = require("../models/BatchInputContentFolder");
const BatchInputJsonNode_1 = require("../models/BatchInputJsonNode");
const BatchInputPage_1 = require("../models/BatchInputPage");
const BatchInputString_1 = require("../models/BatchInputString");
const BatchResponseContentFolder_1 = require("../models/BatchResponseContentFolder");
const BatchResponseContentFolderWithErrors_1 = require("../models/BatchResponseContentFolderWithErrors");
const BatchResponsePage_1 = require("../models/BatchResponsePage");
const BatchResponsePageWithErrors_1 = require("../models/BatchResponsePageWithErrors");
const CollectionResponseWithTotalContentFolderForwardPaging_1 = require("../models/CollectionResponseWithTotalContentFolderForwardPaging");
const CollectionResponseWithTotalPageForwardPaging_1 = require("../models/CollectionResponseWithTotalPageForwardPaging");
const CollectionResponseWithTotalVersionContentFolder_1 = require("../models/CollectionResponseWithTotalVersionContentFolder");
const CollectionResponseWithTotalVersionPage_1 = require("../models/CollectionResponseWithTotalVersionPage");
const ColorStop_1 = require("../models/ColorStop");
const ContentCloneRequestVNext_1 = require("../models/ContentCloneRequestVNext");
const ContentFolder_1 = require("../models/ContentFolder");
const ContentLanguageCloneRequestVNext_1 = require("../models/ContentLanguageCloneRequestVNext");
const ContentLanguageVariation_1 = require("../models/ContentLanguageVariation");
const ContentScheduleRequestVNext_1 = require("../models/ContentScheduleRequestVNext");
const DetachFromLangGroupRequestVNext_1 = require("../models/DetachFromLangGroupRequestVNext");
const ErrorDetail_1 = require("../models/ErrorDetail");
const ForwardPaging_1 = require("../models/ForwardPaging");
const Gradient_1 = require("../models/Gradient");
const LayoutSection_1 = require("../models/LayoutSection");
const ModelError_1 = require("../models/ModelError");
const NextPage_1 = require("../models/NextPage");
const Page_1 = require("../models/Page");
const Paging_1 = require("../models/Paging");
const PreviousPage_1 = require("../models/PreviousPage");
const RGBAColor_1 = require("../models/RGBAColor");
const RowMetaData_1 = require("../models/RowMetaData");
const SetNewLanguagePrimaryRequestVNext_1 = require("../models/SetNewLanguagePrimaryRequestVNext");
const SideOrCorner_1 = require("../models/SideOrCorner");
const StandardError_1 = require("../models/StandardError");
const Styles_1 = require("../models/Styles");
const UpdateLanguagesRequestVNext_1 = require("../models/UpdateLanguagesRequestVNext");
const VersionContentFolder_1 = require("../models/VersionContentFolder");
const VersionPage_1 = require("../models/VersionPage");
const VersionUser_1 = require("../models/VersionUser");
let primitives = [
    "string",
    "boolean",
    "double",
    "integer",
    "long",
    "float",
    "number",
    "any"
];
let enumsMap = new Set([
    "BatchResponseContentFolderStatusEnum",
    "BatchResponseContentFolderWithErrorsStatusEnum",
    "BatchResponsePageStatusEnum",
    "BatchResponsePageWithErrorsStatusEnum",
    "PageLanguageEnum",
    "PageContentTypeCategoryEnum",
    "PageAbStatusEnum",
    "PageCurrentStateEnum",
]);
let typeMap = {
    "AbTestCreateRequestVNext": AbTestCreateRequestVNext_1.AbTestCreateRequestVNext,
    "AbTestEndRequestVNext": AbTestEndRequestVNext_1.AbTestEndRequestVNext,
    "AbTestRerunRequestVNext": AbTestRerunRequestVNext_1.AbTestRerunRequestVNext,
    "Angle": Angle_1.Angle,
    "AttachToLangPrimaryRequestVNext": AttachToLangPrimaryRequestVNext_1.AttachToLangPrimaryRequestVNext,
    "BackgroundImage": BackgroundImage_1.BackgroundImage,
    "BatchInputContentFolder": BatchInputContentFolder_1.BatchInputContentFolder,
    "BatchInputJsonNode": BatchInputJsonNode_1.BatchInputJsonNode,
    "BatchInputPage": BatchInputPage_1.BatchInputPage,
    "BatchInputString": BatchInputString_1.BatchInputString,
    "BatchResponseContentFolder": BatchResponseContentFolder_1.BatchResponseContentFolder,
    "BatchResponseContentFolderWithErrors": BatchResponseContentFolderWithErrors_1.BatchResponseContentFolderWithErrors,
    "BatchResponsePage": BatchResponsePage_1.BatchResponsePage,
    "BatchResponsePageWithErrors": BatchResponsePageWithErrors_1.BatchResponsePageWithErrors,
    "CollectionResponseWithTotalContentFolderForwardPaging": CollectionResponseWithTotalContentFolderForwardPaging_1.CollectionResponseWithTotalContentFolderForwardPaging,
    "CollectionResponseWithTotalPageForwardPaging": CollectionResponseWithTotalPageForwardPaging_1.CollectionResponseWithTotalPageForwardPaging,
    "CollectionResponseWithTotalVersionContentFolder": CollectionResponseWithTotalVersionContentFolder_1.CollectionResponseWithTotalVersionContentFolder,
    "CollectionResponseWithTotalVersionPage": CollectionResponseWithTotalVersionPage_1.CollectionResponseWithTotalVersionPage,
    "ColorStop": ColorStop_1.ColorStop,
    "ContentCloneRequestVNext": ContentCloneRequestVNext_1.ContentCloneRequestVNext,
    "ContentFolder": ContentFolder_1.ContentFolder,
    "ContentLanguageCloneRequestVNext": ContentLanguageCloneRequestVNext_1.ContentLanguageCloneRequestVNext,
    "ContentLanguageVariation": ContentLanguageVariation_1.ContentLanguageVariation,
    "ContentScheduleRequestVNext": ContentScheduleRequestVNext_1.ContentScheduleRequestVNext,
    "DetachFromLangGroupRequestVNext": DetachFromLangGroupRequestVNext_1.DetachFromLangGroupRequestVNext,
    "ErrorDetail": ErrorDetail_1.ErrorDetail,
    "ForwardPaging": ForwardPaging_1.ForwardPaging,
    "Gradient": Gradient_1.Gradient,
    "LayoutSection": LayoutSection_1.LayoutSection,
    "ModelError": ModelError_1.ModelError,
    "NextPage": NextPage_1.NextPage,
    "Page": Page_1.Page,
    "Paging": Paging_1.Paging,
    "PreviousPage": PreviousPage_1.PreviousPage,
    "RGBAColor": RGBAColor_1.RGBAColor,
    "RowMetaData": RowMetaData_1.RowMetaData,
    "SetNewLanguagePrimaryRequestVNext": SetNewLanguagePrimaryRequestVNext_1.SetNewLanguagePrimaryRequestVNext,
    "SideOrCorner": SideOrCorner_1.SideOrCorner,
    "StandardError": StandardError_1.StandardError,
    "Styles": Styles_1.Styles,
    "UpdateLanguagesRequestVNext": UpdateLanguagesRequestVNext_1.UpdateLanguagesRequestVNext,
    "VersionContentFolder": VersionContentFolder_1.VersionContentFolder,
    "VersionPage": VersionPage_1.VersionPage,
    "VersionUser": VersionUser_1.VersionUser,
};
const parseMimeType = (mimeType) => {
    const [type = '', subtype = ''] = mimeType.split('/');
    return {
        type,
        subtype,
        subtypeTokens: subtype.split('+'),
    };
};
const mimeTypePredicateFactory = (predicate) => (mimeType) => predicate(parseMimeType(mimeType));
const mimeTypeSimplePredicateFactory = (type, subtype) => mimeTypePredicateFactory((descriptor) => {
    if (descriptor.type !== type)
        return false;
    if (subtype != null && descriptor.subtype !== subtype)
        return false;
    return true;
});
const isTextLikeMimeType = mimeTypeSimplePredicateFactory('text');
const isJsonMimeType = mimeTypeSimplePredicateFactory('application', 'json');
const isJsonLikeMimeType = mimeTypePredicateFactory((descriptor) => descriptor.type === 'application' && descriptor.subtypeTokens.some((item) => item === 'json'));
const isOctetStreamMimeType = mimeTypeSimplePredicateFactory('application', 'octet-stream');
const isFormUrlencodedMimeType = mimeTypeSimplePredicateFactory('application', 'x-www-form-urlencoded');
const supportedMimeTypePredicatesWithPriority = [
    isJsonMimeType,
    isJsonLikeMimeType,
    isTextLikeMimeType,
    isOctetStreamMimeType,
    isFormUrlencodedMimeType,
];
const nullableSuffix = " | null";
const optionalSuffix = " | undefined";
const arrayPrefix = "Array<";
const arraySuffix = ">";
const mapPrefix = "{ [key: string]: ";
const mapSuffix = "; }";
class ObjectSerializer {
    static findCorrectType(data, expectedType) {
        if (data == undefined) {
            return expectedType;
        }
        else if (primitives.indexOf(expectedType.toLowerCase()) !== -1) {
            return expectedType;
        }
        else if (expectedType === "Date") {
            return expectedType;
        }
        else {
            if (enumsMap.has(expectedType)) {
                return expectedType;
            }
            if (!typeMap[expectedType]) {
                return expectedType;
            }
            let discriminatorProperty = typeMap[expectedType].discriminator;
            if (discriminatorProperty == null) {
                return expectedType;
            }
            else {
                if (data[discriminatorProperty]) {
                    var discriminatorType = data[discriminatorProperty];
                    let mapping = typeMap[expectedType].mapping;
                    if (mapping != undefined && mapping[discriminatorType]) {
                        return mapping[discriminatorType];
                    }
                    else if (typeMap[discriminatorType]) {
                        return discriminatorType;
                    }
                    else {
                        return expectedType;
                    }
                }
                else {
                    return expectedType;
                }
            }
        }
    }
    static serialize(data, type, format) {
        if (data == undefined) {
            return data;
        }
        else if (primitives.indexOf(type.toLowerCase()) !== -1) {
            return data;
        }
        else if (type.endsWith(nullableSuffix)) {
            let subType = type.slice(0, -nullableSuffix.length);
            return ObjectSerializer.serialize(data, subType, format);
        }
        else if (type.endsWith(optionalSuffix)) {
            let subType = type.slice(0, -optionalSuffix.length);
            return ObjectSerializer.serialize(data, subType, format);
        }
        else if (type.startsWith(arrayPrefix)) {
            let subType = type.slice(arrayPrefix.length, -arraySuffix.length);
            let transformedData = [];
            for (let date of data) {
                transformedData.push(ObjectSerializer.serialize(date, subType, format));
            }
            return transformedData;
        }
        else if (type.startsWith(mapPrefix)) {
            let subType = type.slice(mapPrefix.length, -mapSuffix.length);
            let transformedData = {};
            for (let key in data) {
                transformedData[key] = ObjectSerializer.serialize(data[key], subType, format);
            }
            return transformedData;
        }
        else if (type === "Date") {
            if (format == "date") {
                let month = data.getMonth() + 1;
                month = month < 10 ? "0" + month.toString() : month.toString();
                let day = data.getDate();
                day = day < 10 ? "0" + day.toString() : day.toString();
                return data.getFullYear() + "-" + month + "-" + day;
            }
            else {
                return data.toISOString();
            }
        }
        else {
            if (enumsMap.has(type)) {
                return data;
            }
            if (!typeMap[type]) {
                return data;
            }
            type = this.findCorrectType(data, type);
            let attributeTypes = typeMap[type].getAttributeTypeMap();
            let instance = {};
            for (let attributeType of attributeTypes) {
                instance[attributeType.baseName] = ObjectSerializer.serialize(data[attributeType.name], attributeType.type, attributeType.format);
            }
            return instance;
        }
    }
    static deserialize(data, type, format) {
        type = ObjectSerializer.findCorrectType(data, type);
        if (data == undefined) {
            return data;
        }
        else if (primitives.indexOf(type.toLowerCase()) !== -1) {
            return data;
        }
        else if (type.endsWith(nullableSuffix)) {
            let subType = type.slice(0, -nullableSuffix.length);
            return ObjectSerializer.deserialize(data, subType, format);
        }
        else if (type.endsWith(optionalSuffix)) {
            let subType = type.slice(0, -optionalSuffix.length);
            return ObjectSerializer.deserialize(data, subType, format);
        }
        else if (type.startsWith(arrayPrefix)) {
            let subType = type.slice(arrayPrefix.length, -arraySuffix.length);
            let transformedData = [];
            for (let date of data) {
                transformedData.push(ObjectSerializer.deserialize(date, subType, format));
            }
            return transformedData;
        }
        else if (type.startsWith(mapPrefix)) {
            let subType = type.slice(mapPrefix.length, -mapSuffix.length);
            let transformedData = {};
            for (let key in data) {
                transformedData[key] = ObjectSerializer.deserialize(data[key], subType, format);
            }
            return transformedData;
        }
        else if (type === "Date") {
            return new Date(data);
        }
        else {
            if (enumsMap.has(type)) {
                return data;
            }
            if (!typeMap[type]) {
                return data;
            }
            let instance = new typeMap[type]();
            let attributeTypes = typeMap[type].getAttributeTypeMap();
            for (let attributeType of attributeTypes) {
                let value = ObjectSerializer.deserialize(data[attributeType.baseName], attributeType.type, attributeType.format);
                if (value !== undefined) {
                    instance[attributeType.name] = value;
                }
            }
            return instance;
        }
    }
    static normalizeMediaType(mediaType) {
        var _a;
        if (mediaType === undefined) {
            return undefined;
        }
        return ((_a = mediaType.split(";")[0]) !== null && _a !== void 0 ? _a : '').trim().toLowerCase();
    }
    static getPreferredMediaType(mediaTypes) {
        if (mediaTypes.length === 0) {
            return "application/json";
        }
        const normalMediaTypes = mediaTypes.map(ObjectSerializer.normalizeMediaType);
        for (const predicate of supportedMimeTypePredicatesWithPriority) {
            for (const mediaType of normalMediaTypes) {
                if (mediaType != null && predicate(mediaType)) {
                    return mediaType;
                }
            }
        }
        throw new Error("None of the given media types are supported: " + mediaTypes.join(", "));
    }
    static stringify(data, mediaType) {
        if (isTextLikeMimeType(mediaType)) {
            return String(data);
        }
        if (isJsonLikeMimeType(mediaType)) {
            return JSON.stringify(data);
        }
        throw new Error("The mediaType " + mediaType + " is not supported by ObjectSerializer.stringify.");
    }
    static parse(rawData, mediaType) {
        if (mediaType === undefined) {
            throw new Error("Cannot parse content. No Content-Type defined.");
        }
        if (isTextLikeMimeType(mediaType)) {
            return rawData;
        }
        if (isJsonLikeMimeType(mediaType)) {
            return JSON.parse(rawData);
        }
        throw new Error("The mediaType " + mediaType + " is not supported by ObjectSerializer.parse.");
    }
}
exports.ObjectSerializer = ObjectSerializer;
//# sourceMappingURL=ObjectSerializer.js.map