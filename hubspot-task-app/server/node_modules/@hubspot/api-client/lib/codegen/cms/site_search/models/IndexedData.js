"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexedDataTypeEnum = exports.IndexedData = void 0;
class IndexedData {
    static getAttributeTypeMap() {
        return IndexedData.attributeTypeMap;
    }
    constructor() {
    }
}
exports.IndexedData = IndexedData;
IndexedData.discriminator = undefined;
IndexedData.mapping = undefined;
IndexedData.attributeTypeMap = [
    {
        "name": "id",
        "baseName": "id",
        "type": "string",
        "format": ""
    },
    {
        "name": "type",
        "baseName": "type",
        "type": "IndexedDataTypeEnum",
        "format": ""
    },
    {
        "name": "fields",
        "baseName": "fields",
        "type": "{ [key: string]: IndexedField; }",
        "format": ""
    }
];
var IndexedDataTypeEnum;
(function (IndexedDataTypeEnum) {
    IndexedDataTypeEnum["LandingPage"] = "LANDING_PAGE";
    IndexedDataTypeEnum["BlogPost"] = "BLOG_POST";
    IndexedDataTypeEnum["SitePage"] = "SITE_PAGE";
    IndexedDataTypeEnum["KnowledgeArticle"] = "KNOWLEDGE_ARTICLE";
    IndexedDataTypeEnum["ListingPage"] = "LISTING_PAGE";
})(IndexedDataTypeEnum = exports.IndexedDataTypeEnum || (exports.IndexedDataTypeEnum = {}));
//# sourceMappingURL=IndexedData.js.map