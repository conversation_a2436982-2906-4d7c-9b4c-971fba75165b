import { HttpLibrary } from "./http/http";
import { Middleware, PromiseMiddleware } from "./middleware";
import { BaseServerConfiguration } from "./servers";
import { AuthMethods, AuthMethodsConfiguration } from "./auth/auth";
export interface Configuration<M = Middleware> {
    readonly baseServer: BaseServerConfiguration;
    readonly httpApi: HttpLibrary;
    readonly middleware: M[];
    readonly authMethods: AuthMethods;
}
export interface MiddlewareMergeOptions {
    middlewareMergeStrategy?: 'replace' | 'append' | 'prepend';
}
export type ConfigurationOptions<M = Middleware> = Partial<Configuration<M>> & MiddlewareMergeOptions;
export type StandardConfigurationOptions = ConfigurationOptions<Middleware>;
export type PromiseConfigurationOptions = ConfigurationOptions<PromiseMiddleware>;
export interface ConfigurationParameters {
    baseServer?: BaseServerConfiguration;
    httpApi?: HttpLibrary;
    middleware?: Middleware[];
    promiseMiddleware?: PromiseMiddleware[];
    authMethods?: AuthMethodsConfiguration;
}
export declare function createConfiguration(conf?: ConfigurationParameters): Configuration;
