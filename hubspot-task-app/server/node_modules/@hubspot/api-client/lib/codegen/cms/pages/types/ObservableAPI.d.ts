import { HttpInfo } from '../http/http';
import { Configuration, ConfigurationOptions } from '../configuration';
import { Observable } from '../rxjsStub';
import { AbTestCreateRequestVNext } from '../models/AbTestCreateRequestVNext';
import { AbTestEndRequestVNext } from '../models/AbTestEndRequestVNext';
import { AbTestRerunRequestVNext } from '../models/AbTestRerunRequestVNext';
import { AttachToLangPrimaryRequestVNext } from '../models/AttachToLangPrimaryRequestVNext';
import { BatchInputContentFolder } from '../models/BatchInputContentFolder';
import { BatchInputJsonNode } from '../models/BatchInputJsonNode';
import { BatchInputPage } from '../models/BatchInputPage';
import { BatchInputString } from '../models/BatchInputString';
import { BatchResponseContentFolder } from '../models/BatchResponseContentFolder';
import { BatchResponseContentFolderWithErrors } from '../models/BatchResponseContentFolderWithErrors';
import { BatchResponsePage } from '../models/BatchResponsePage';
import { BatchResponsePageWithErrors } from '../models/BatchResponsePageWithErrors';
import { CollectionResponseWithTotalContentFolderForwardPaging } from '../models/CollectionResponseWithTotalContentFolderForwardPaging';
import { CollectionResponseWithTotalPageForwardPaging } from '../models/CollectionResponseWithTotalPageForwardPaging';
import { CollectionResponseWithTotalVersionContentFolder } from '../models/CollectionResponseWithTotalVersionContentFolder';
import { CollectionResponseWithTotalVersionPage } from '../models/CollectionResponseWithTotalVersionPage';
import { ContentCloneRequestVNext } from '../models/ContentCloneRequestVNext';
import { ContentFolder } from '../models/ContentFolder';
import { ContentLanguageCloneRequestVNext } from '../models/ContentLanguageCloneRequestVNext';
import { ContentScheduleRequestVNext } from '../models/ContentScheduleRequestVNext';
import { DetachFromLangGroupRequestVNext } from '../models/DetachFromLangGroupRequestVNext';
import { Page } from '../models/Page';
import { SetNewLanguagePrimaryRequestVNext } from '../models/SetNewLanguagePrimaryRequestVNext';
import { UpdateLanguagesRequestVNext } from '../models/UpdateLanguagesRequestVNext';
import { VersionContentFolder } from '../models/VersionContentFolder';
import { VersionPage } from '../models/VersionPage';
import { LandingPagesApiRequestFactory, LandingPagesApiResponseProcessor } from "../apis/LandingPagesApi";
export declare class ObservableLandingPagesApi {
    private requestFactory;
    private responseProcessor;
    private configuration;
    constructor(configuration: Configuration, requestFactory?: LandingPagesApiRequestFactory, responseProcessor?: LandingPagesApiResponseProcessor);
    archiveWithHttpInfo(objectId: string, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    archive(objectId: string, archived?: boolean, _options?: ConfigurationOptions): Observable<void>;
    archiveBatchWithHttpInfo(batchInputString: BatchInputString, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    archiveBatch(batchInputString: BatchInputString, _options?: ConfigurationOptions): Observable<void>;
    archiveFolderWithHttpInfo(objectId: string, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    archiveFolder(objectId: string, archived?: boolean, _options?: ConfigurationOptions): Observable<void>;
    archiveFoldersWithHttpInfo(batchInputString: BatchInputString, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    archiveFolders(batchInputString: BatchInputString, _options?: ConfigurationOptions): Observable<void>;
    attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    attachToLangGroup(attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    cloneWithHttpInfo(contentCloneRequestVNext: ContentCloneRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    clone(contentCloneRequestVNext: ContentCloneRequestVNext, _options?: ConfigurationOptions): Observable<Page>;
    createWithHttpInfo(page: Page, _options?: ConfigurationOptions): Observable<HttpInfo<void | Page>>;
    create(page: Page, _options?: ConfigurationOptions): Observable<void | Page>;
    createABTestVariationWithHttpInfo(abTestCreateRequestVNext: AbTestCreateRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    createABTestVariation(abTestCreateRequestVNext: AbTestCreateRequestVNext, _options?: ConfigurationOptions): Observable<Page>;
    createBatchWithHttpInfo(batchInputPage: BatchInputPage, _options?: ConfigurationOptions): Observable<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    createBatch(batchInputPage: BatchInputPage, _options?: ConfigurationOptions): Observable<BatchResponsePage | BatchResponsePageWithErrors>;
    createFolderWithHttpInfo(contentFolder: ContentFolder, _options?: ConfigurationOptions): Observable<HttpInfo<ContentFolder>>;
    createFolder(contentFolder: ContentFolder, _options?: ConfigurationOptions): Observable<ContentFolder>;
    createFoldersWithHttpInfo(batchInputContentFolder: BatchInputContentFolder, _options?: ConfigurationOptions): Observable<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    createFolders(batchInputContentFolder: BatchInputContentFolder, _options?: ConfigurationOptions): Observable<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>;
    createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    createLangVariation(contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext, _options?: ConfigurationOptions): Observable<Page>;
    detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    detachFromLangGroup(detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    endActiveABTestWithHttpInfo(abTestEndRequestVNext: AbTestEndRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    endActiveABTest(abTestEndRequestVNext: AbTestEndRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    getByIdWithHttpInfo(objectId: string, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    getById(objectId: string, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<Page>;
    getDraftByIdWithHttpInfo(objectId: string, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    getDraftById(objectId: string, _options?: ConfigurationOptions): Observable<Page>;
    getFolderByIdWithHttpInfo(objectId: string, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<HttpInfo<ContentFolder>>;
    getFolderById(objectId: string, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<ContentFolder>;
    getFolderPreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<HttpInfo<VersionContentFolder>>;
    getFolderPreviousVersion(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<VersionContentFolder>;
    getFolderPreviousVersionsWithHttpInfo(objectId: string, after?: string, before?: string, limit?: number, _options?: ConfigurationOptions): Observable<HttpInfo<CollectionResponseWithTotalVersionContentFolder>>;
    getFolderPreviousVersions(objectId: string, after?: string, before?: string, limit?: number, _options?: ConfigurationOptions): Observable<CollectionResponseWithTotalVersionContentFolder>;
    getFoldersPageWithHttpInfo(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<HttpInfo<CollectionResponseWithTotalContentFolderForwardPaging>>;
    getFoldersPage(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<CollectionResponseWithTotalContentFolderForwardPaging>;
    getPageWithHttpInfo(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<HttpInfo<CollectionResponseWithTotalPageForwardPaging>>;
    getPage(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<CollectionResponseWithTotalPageForwardPaging>;
    getPreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<HttpInfo<VersionPage>>;
    getPreviousVersion(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<VersionPage>;
    getPreviousVersionsWithHttpInfo(objectId: string, after?: string, before?: string, limit?: number, _options?: ConfigurationOptions): Observable<HttpInfo<CollectionResponseWithTotalVersionPage>>;
    getPreviousVersions(objectId: string, after?: string, before?: string, limit?: number, _options?: ConfigurationOptions): Observable<CollectionResponseWithTotalVersionPage>;
    pushLiveWithHttpInfo(objectId: string, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    pushLive(objectId: string, _options?: ConfigurationOptions): Observable<void>;
    readBatchWithHttpInfo(batchInputString: BatchInputString, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    readBatch(batchInputString: BatchInputString, archived?: boolean, _options?: ConfigurationOptions): Observable<BatchResponsePage | BatchResponsePageWithErrors>;
    readFoldersWithHttpInfo(batchInputString: BatchInputString, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    readFolders(batchInputString: BatchInputString, archived?: boolean, _options?: ConfigurationOptions): Observable<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>;
    rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext: AbTestRerunRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    rerunPreviousABTest(abTestRerunRequestVNext: AbTestRerunRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    resetDraftWithHttpInfo(objectId: string, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    resetDraft(objectId: string, _options?: ConfigurationOptions): Observable<void>;
    restoreFolderPreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<HttpInfo<ContentFolder>>;
    restoreFolderPreviousVersion(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<ContentFolder>;
    restorePreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    restorePreviousVersion(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<Page>;
    restorePreviousVersionToDraftWithHttpInfo(objectId: string, revisionId: number, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    restorePreviousVersionToDraft(objectId: string, revisionId: number, _options?: ConfigurationOptions): Observable<Page>;
    scheduleWithHttpInfo(contentScheduleRequestVNext: ContentScheduleRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    schedule(contentScheduleRequestVNext: ContentScheduleRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    setLangPrimary(setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    updateWithHttpInfo(objectId: string, page: Page, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    update(objectId: string, page: Page, archived?: boolean, _options?: ConfigurationOptions): Observable<Page>;
    updateBatchWithHttpInfo(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    updateBatch(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: ConfigurationOptions): Observable<BatchResponsePage | BatchResponsePageWithErrors>;
    updateDraftWithHttpInfo(objectId: string, page: Page, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    updateDraft(objectId: string, page: Page, _options?: ConfigurationOptions): Observable<Page>;
    updateFolderWithHttpInfo(objectId: string, contentFolder: ContentFolder, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<ContentFolder>>;
    updateFolder(objectId: string, contentFolder: ContentFolder, archived?: boolean, _options?: ConfigurationOptions): Observable<ContentFolder>;
    updateFoldersWithHttpInfo(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>>;
    updateFolders(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: ConfigurationOptions): Observable<BatchResponseContentFolder | BatchResponseContentFolderWithErrors>;
    updateLangsWithHttpInfo(updateLanguagesRequestVNext: UpdateLanguagesRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    updateLangs(updateLanguagesRequestVNext: UpdateLanguagesRequestVNext, _options?: ConfigurationOptions): Observable<void>;
}
import { SitePagesApiRequestFactory, SitePagesApiResponseProcessor } from "../apis/SitePagesApi";
export declare class ObservableSitePagesApi {
    private requestFactory;
    private responseProcessor;
    private configuration;
    constructor(configuration: Configuration, requestFactory?: SitePagesApiRequestFactory, responseProcessor?: SitePagesApiResponseProcessor);
    archiveWithHttpInfo(objectId: string, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    archive(objectId: string, archived?: boolean, _options?: ConfigurationOptions): Observable<void>;
    archiveBatchWithHttpInfo(batchInputString: BatchInputString, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    archiveBatch(batchInputString: BatchInputString, _options?: ConfigurationOptions): Observable<void>;
    attachToLangGroupWithHttpInfo(attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    attachToLangGroup(attachToLangPrimaryRequestVNext: AttachToLangPrimaryRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    cloneWithHttpInfo(contentCloneRequestVNext: ContentCloneRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    clone(contentCloneRequestVNext: ContentCloneRequestVNext, _options?: ConfigurationOptions): Observable<Page>;
    createWithHttpInfo(page: Page, _options?: ConfigurationOptions): Observable<HttpInfo<void | Page>>;
    create(page: Page, _options?: ConfigurationOptions): Observable<void | Page>;
    createABTestVariationWithHttpInfo(abTestCreateRequestVNext: AbTestCreateRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    createABTestVariation(abTestCreateRequestVNext: AbTestCreateRequestVNext, _options?: ConfigurationOptions): Observable<Page>;
    createBatchWithHttpInfo(batchInputPage: BatchInputPage, _options?: ConfigurationOptions): Observable<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    createBatch(batchInputPage: BatchInputPage, _options?: ConfigurationOptions): Observable<BatchResponsePage | BatchResponsePageWithErrors>;
    createLangVariationWithHttpInfo(contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    createLangVariation(contentLanguageCloneRequestVNext: ContentLanguageCloneRequestVNext, _options?: ConfigurationOptions): Observable<Page>;
    detachFromLangGroupWithHttpInfo(detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    detachFromLangGroup(detachFromLangGroupRequestVNext: DetachFromLangGroupRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    endActiveABTestWithHttpInfo(abTestEndRequestVNext: AbTestEndRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    endActiveABTest(abTestEndRequestVNext: AbTestEndRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    getByIdWithHttpInfo(objectId: string, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    getById(objectId: string, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<Page>;
    getDraftByIdWithHttpInfo(objectId: string, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    getDraftById(objectId: string, _options?: ConfigurationOptions): Observable<Page>;
    getPageWithHttpInfo(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<HttpInfo<CollectionResponseWithTotalPageForwardPaging>>;
    getPage(createdAt?: Date, createdAfter?: Date, createdBefore?: Date, updatedAt?: Date, updatedAfter?: Date, updatedBefore?: Date, sort?: Array<string>, after?: string, limit?: number, archived?: boolean, property?: string, _options?: ConfigurationOptions): Observable<CollectionResponseWithTotalPageForwardPaging>;
    getPreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<HttpInfo<VersionPage>>;
    getPreviousVersion(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<VersionPage>;
    getPreviousVersionsWithHttpInfo(objectId: string, after?: string, before?: string, limit?: number, _options?: ConfigurationOptions): Observable<HttpInfo<CollectionResponseWithTotalVersionPage>>;
    getPreviousVersions(objectId: string, after?: string, before?: string, limit?: number, _options?: ConfigurationOptions): Observable<CollectionResponseWithTotalVersionPage>;
    pushLiveWithHttpInfo(objectId: string, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    pushLive(objectId: string, _options?: ConfigurationOptions): Observable<void>;
    readBatchWithHttpInfo(batchInputString: BatchInputString, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    readBatch(batchInputString: BatchInputString, archived?: boolean, _options?: ConfigurationOptions): Observable<BatchResponsePage | BatchResponsePageWithErrors>;
    rerunPreviousABTestWithHttpInfo(abTestRerunRequestVNext: AbTestRerunRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    rerunPreviousABTest(abTestRerunRequestVNext: AbTestRerunRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    resetDraftWithHttpInfo(objectId: string, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    resetDraft(objectId: string, _options?: ConfigurationOptions): Observable<void>;
    restorePreviousVersionWithHttpInfo(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    restorePreviousVersion(objectId: string, revisionId: string, _options?: ConfigurationOptions): Observable<Page>;
    restorePreviousVersionToDraftWithHttpInfo(objectId: string, revisionId: number, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    restorePreviousVersionToDraft(objectId: string, revisionId: number, _options?: ConfigurationOptions): Observable<Page>;
    scheduleWithHttpInfo(contentScheduleRequestVNext: ContentScheduleRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    schedule(contentScheduleRequestVNext: ContentScheduleRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    setLangPrimaryWithHttpInfo(setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    setLangPrimary(setNewLanguagePrimaryRequestVNext: SetNewLanguagePrimaryRequestVNext, _options?: ConfigurationOptions): Observable<void>;
    updateWithHttpInfo(objectId: string, page: Page, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    update(objectId: string, page: Page, archived?: boolean, _options?: ConfigurationOptions): Observable<Page>;
    updateBatchWithHttpInfo(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: ConfigurationOptions): Observable<HttpInfo<BatchResponsePage | BatchResponsePageWithErrors>>;
    updateBatch(batchInputJsonNode: BatchInputJsonNode, archived?: boolean, _options?: ConfigurationOptions): Observable<BatchResponsePage | BatchResponsePageWithErrors>;
    updateDraftWithHttpInfo(objectId: string, page: Page, _options?: ConfigurationOptions): Observable<HttpInfo<Page>>;
    updateDraft(objectId: string, page: Page, _options?: ConfigurationOptions): Observable<Page>;
    updateLangsWithHttpInfo(updateLanguagesRequestVNext: UpdateLanguagesRequestVNext, _options?: ConfigurationOptions): Observable<HttpInfo<void>>;
    updateLangs(updateLanguagesRequestVNext: UpdateLanguagesRequestVNext, _options?: ConfigurationOptions): Observable<void>;
}
