"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentLanguageCloneRequestVNext = void 0;
class ContentLanguageCloneRequestVNext {
    static getAttributeTypeMap() {
        return ContentLanguageCloneRequestVNext.attributeTypeMap;
    }
    constructor() {
    }
}
exports.ContentLanguageCloneRequestVNext = ContentLanguageCloneRequestVNext;
ContentLanguageCloneRequestVNext.discriminator = undefined;
ContentLanguageCloneRequestVNext.mapping = undefined;
ContentLanguageCloneRequestVNext.attributeTypeMap = [
    {
        "name": "language",
        "baseName": "language",
        "type": "string",
        "format": ""
    },
    {
        "name": "id",
        "baseName": "id",
        "type": "string",
        "format": ""
    },
    {
        "name": "primaryLanguage",
        "baseName": "primaryLanguage",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=ContentLanguageCloneRequestVNext.js.map