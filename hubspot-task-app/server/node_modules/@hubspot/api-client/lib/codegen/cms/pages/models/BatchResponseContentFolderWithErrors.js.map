{"version": 3, "file": "BatchResponseContentFolderWithErrors.js", "sourceRoot": "", "sources": ["../../../../../codegen/cms/pages/models/BatchResponseContentFolderWithErrors.ts"], "names": [], "mappings": ";;;AAkBA,MAAa,oCAAoC;IAwF7C,MAAM,CAAC,mBAAmB;QACtB,OAAO,oCAAoC,CAAC,gBAAgB,CAAC;IACjE,CAAC;IAED;IACA,CAAC;;AA7FL,oFA8FC;AA5DmB,kDAAa,GAAuB,SAAS,CAAC;AAE9C,4CAAO,GAA0C,SAAS,CAAC;AAE3D,qDAAgB,GAA0E;IACtG;QACI,MAAM,EAAE,aAAa;QACrB,UAAU,EAAE,aAAa;QACzB,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,WAAW;KACxB;IACD;QACI,MAAM,EAAE,WAAW;QACnB,UAAU,EAAE,WAAW;QACvB,MAAM,EAAE,QAAQ;QAChB,QAAQ,EAAE,OAAO;KACpB;IACD;QACI,MAAM,EAAE,aAAa;QACrB,UAAU,EAAE,aAAa;QACzB,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,WAAW;KACxB;IACD;QACI,MAAM,EAAE,WAAW;QACnB,UAAU,EAAE,WAAW;QACvB,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,WAAW;KACxB;IACD;QACI,MAAM,EAAE,OAAO;QACf,UAAU,EAAE,OAAO;QACnB,MAAM,EAAE,4BAA4B;QACpC,QAAQ,EAAE,EAAE;KACf;IACD;QACI,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,SAAS;QACrB,MAAM,EAAE,sBAAsB;QAC9B,QAAQ,EAAE,EAAE;KACf;IACD;QACI,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,QAAQ;QACpB,MAAM,EAAE,sBAAsB;QAC9B,QAAQ,EAAE,EAAE;KACf;IACD;QACI,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,QAAQ;QACpB,MAAM,EAAE,gDAAgD;QACxD,QAAQ,EAAE,EAAE;KACf;CAAK,CAAC;AAUf,IAAY,8CAKX;AALD,WAAY,8CAA8C;IACtD,qEAAmB,CAAA;IACnB,2EAAyB,CAAA;IACzB,uEAAqB,CAAA;IACrB,uEAAqB,CAAA;AACzB,CAAC,EALW,8CAA8C,GAA9C,sDAA8C,KAA9C,sDAA8C,QAKzD"}