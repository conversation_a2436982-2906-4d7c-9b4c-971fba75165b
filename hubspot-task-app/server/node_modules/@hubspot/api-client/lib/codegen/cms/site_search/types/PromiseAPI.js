"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromisePublicApi = void 0;
const middleware_1 = require("../middleware");
const ObservableAPI_1 = require("./ObservableAPI");
class PromisePublicApi {
    constructor(configuration, requestFactory, responseProcessor) {
        this.api = new ObservableAPI_1.ObservablePublicApi(configuration, requestFactory, responseProcessor);
    }
    getByIdWithHttpInfo(contentId, type, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getByIdWithHttpInfo(contentId, type, observableOptions);
        return result.toPromise();
    }
    getById(contentId, type, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.getById(contentId, type, observableOptions);
        return result.toPromise();
    }
    searchWithHttpInfo(q, limit, offset, language, matchPrefix, autocomplete, popularityBoost, boostLimit, boostRecent, tableId, hubdbQuery, domain, type, pathPrefix, property, length, groupId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.searchWithHttpInfo(q, limit, offset, language, matchPrefix, autocomplete, popularityBoost, boostLimit, boostRecent, tableId, hubdbQuery, domain, type, pathPrefix, property, length, groupId, observableOptions);
        return result.toPromise();
    }
    search(q, limit, offset, language, matchPrefix, autocomplete, popularityBoost, boostLimit, boostRecent, tableId, hubdbQuery, domain, type, pathPrefix, property, length, groupId, _options) {
        var _a;
        let observableOptions;
        if (_options) {
            observableOptions = {
                baseServer: _options.baseServer,
                httpApi: _options.httpApi,
                middleware: (_a = _options.middleware) === null || _a === void 0 ? void 0 : _a.map(m => new middleware_1.PromiseMiddlewareWrapper(m)),
                middlewareMergeStrategy: _options.middlewareMergeStrategy,
                authMethods: _options.authMethods
            };
        }
        const result = this.api.search(q, limit, offset, language, matchPrefix, autocomplete, popularityBoost, boostLimit, boostRecent, tableId, hubdbQuery, domain, type, pathPrefix, property, length, groupId, observableOptions);
        return result.toPromise();
    }
}
exports.PromisePublicApi = PromisePublicApi;
//# sourceMappingURL=PromiseAPI.js.map