"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentSearchResultTypeEnum = exports.ContentSearchResultLanguageEnum = exports.ContentSearchResult = void 0;
class ContentSearchResult {
    static getAttributeTypeMap() {
        return ContentSearchResult.attributeTypeMap;
    }
    constructor() {
    }
}
exports.ContentSearchResult = ContentSearchResult;
ContentSearchResult.discriminator = undefined;
ContentSearchResult.mapping = undefined;
ContentSearchResult.attributeTypeMap = [
    {
        "name": "combinedId",
        "baseName": "combinedId",
        "type": "string",
        "format": ""
    },
    {
        "name": "description",
        "baseName": "description",
        "type": "string",
        "format": ""
    },
    {
        "name": "language",
        "baseName": "language",
        "type": "ContentSearchResultLanguageEnum",
        "format": ""
    },
    {
        "name": "type",
        "baseName": "type",
        "type": "ContentSearchResultTypeEnum",
        "format": ""
    },
    {
        "name": "title",
        "baseName": "title",
        "type": "string",
        "format": ""
    },
    {
        "name": "url",
        "baseName": "url",
        "type": "string",
        "format": ""
    },
    {
        "name": "tags",
        "baseName": "tags",
        "type": "Array<string>",
        "format": ""
    },
    {
        "name": "rowId",
        "baseName": "rowId",
        "type": "number",
        "format": "int64"
    },
    {
        "name": "featuredImageUrl",
        "baseName": "featuredImageUrl",
        "type": "string",
        "format": ""
    },
    {
        "name": "score",
        "baseName": "score",
        "type": "number",
        "format": ""
    },
    {
        "name": "authorFullName",
        "baseName": "authorFullName",
        "type": "string",
        "format": ""
    },
    {
        "name": "domain",
        "baseName": "domain",
        "type": "string",
        "format": ""
    },
    {
        "name": "tableId",
        "baseName": "tableId",
        "type": "number",
        "format": "int64"
    },
    {
        "name": "id",
        "baseName": "id",
        "type": "number",
        "format": "int32"
    },
    {
        "name": "publishedDate",
        "baseName": "publishedDate",
        "type": "number",
        "format": "int64"
    },
    {
        "name": "category",
        "baseName": "category",
        "type": "string",
        "format": ""
    },
    {
        "name": "subcategory",
        "baseName": "subcategory",
        "type": "string",
        "format": ""
    }
];
var ContentSearchResultLanguageEnum;
(function (ContentSearchResultLanguageEnum) {
    ContentSearchResultLanguageEnum["Af"] = "af";
    ContentSearchResultLanguageEnum["AfNa"] = "af-na";
    ContentSearchResultLanguageEnum["AfZa"] = "af-za";
    ContentSearchResultLanguageEnum["Agq"] = "agq";
    ContentSearchResultLanguageEnum["AgqCm"] = "agq-cm";
    ContentSearchResultLanguageEnum["Ak"] = "ak";
    ContentSearchResultLanguageEnum["AkGh"] = "ak-gh";
    ContentSearchResultLanguageEnum["Am"] = "am";
    ContentSearchResultLanguageEnum["AmEt"] = "am-et";
    ContentSearchResultLanguageEnum["Ar"] = "ar";
    ContentSearchResultLanguageEnum["Ar001"] = "ar-001";
    ContentSearchResultLanguageEnum["ArAe"] = "ar-ae";
    ContentSearchResultLanguageEnum["ArBh"] = "ar-bh";
    ContentSearchResultLanguageEnum["ArDj"] = "ar-dj";
    ContentSearchResultLanguageEnum["ArDz"] = "ar-dz";
    ContentSearchResultLanguageEnum["ArEg"] = "ar-eg";
    ContentSearchResultLanguageEnum["ArEh"] = "ar-eh";
    ContentSearchResultLanguageEnum["ArEr"] = "ar-er";
    ContentSearchResultLanguageEnum["ArIl"] = "ar-il";
    ContentSearchResultLanguageEnum["ArIq"] = "ar-iq";
    ContentSearchResultLanguageEnum["ArJo"] = "ar-jo";
    ContentSearchResultLanguageEnum["ArKm"] = "ar-km";
    ContentSearchResultLanguageEnum["ArKw"] = "ar-kw";
    ContentSearchResultLanguageEnum["ArLb"] = "ar-lb";
    ContentSearchResultLanguageEnum["ArLy"] = "ar-ly";
    ContentSearchResultLanguageEnum["ArMa"] = "ar-ma";
    ContentSearchResultLanguageEnum["ArMr"] = "ar-mr";
    ContentSearchResultLanguageEnum["ArOm"] = "ar-om";
    ContentSearchResultLanguageEnum["ArPs"] = "ar-ps";
    ContentSearchResultLanguageEnum["ArQa"] = "ar-qa";
    ContentSearchResultLanguageEnum["ArSa"] = "ar-sa";
    ContentSearchResultLanguageEnum["ArSd"] = "ar-sd";
    ContentSearchResultLanguageEnum["ArSo"] = "ar-so";
    ContentSearchResultLanguageEnum["ArSs"] = "ar-ss";
    ContentSearchResultLanguageEnum["ArSy"] = "ar-sy";
    ContentSearchResultLanguageEnum["ArTd"] = "ar-td";
    ContentSearchResultLanguageEnum["ArTn"] = "ar-tn";
    ContentSearchResultLanguageEnum["ArYe"] = "ar-ye";
    ContentSearchResultLanguageEnum["As"] = "as";
    ContentSearchResultLanguageEnum["AsIn"] = "as-in";
    ContentSearchResultLanguageEnum["Asa"] = "asa";
    ContentSearchResultLanguageEnum["AsaTz"] = "asa-tz";
    ContentSearchResultLanguageEnum["Ast"] = "ast";
    ContentSearchResultLanguageEnum["AstEs"] = "ast-es";
    ContentSearchResultLanguageEnum["Az"] = "az";
    ContentSearchResultLanguageEnum["AzAz"] = "az-az";
    ContentSearchResultLanguageEnum["Bas"] = "bas";
    ContentSearchResultLanguageEnum["BasCm"] = "bas-cm";
    ContentSearchResultLanguageEnum["Be"] = "be";
    ContentSearchResultLanguageEnum["BeBy"] = "be-by";
    ContentSearchResultLanguageEnum["Bem"] = "bem";
    ContentSearchResultLanguageEnum["BemZm"] = "bem-zm";
    ContentSearchResultLanguageEnum["Bez"] = "bez";
    ContentSearchResultLanguageEnum["BezTz"] = "bez-tz";
    ContentSearchResultLanguageEnum["Bg"] = "bg";
    ContentSearchResultLanguageEnum["BgBg"] = "bg-bg";
    ContentSearchResultLanguageEnum["Bm"] = "bm";
    ContentSearchResultLanguageEnum["BmMl"] = "bm-ml";
    ContentSearchResultLanguageEnum["Bn"] = "bn";
    ContentSearchResultLanguageEnum["BnBd"] = "bn-bd";
    ContentSearchResultLanguageEnum["BnIn"] = "bn-in";
    ContentSearchResultLanguageEnum["Bo"] = "bo";
    ContentSearchResultLanguageEnum["BoCn"] = "bo-cn";
    ContentSearchResultLanguageEnum["BoIn"] = "bo-in";
    ContentSearchResultLanguageEnum["Br"] = "br";
    ContentSearchResultLanguageEnum["BrFr"] = "br-fr";
    ContentSearchResultLanguageEnum["Brx"] = "brx";
    ContentSearchResultLanguageEnum["BrxIn"] = "brx-in";
    ContentSearchResultLanguageEnum["Bs"] = "bs";
    ContentSearchResultLanguageEnum["BsBa"] = "bs-ba";
    ContentSearchResultLanguageEnum["Ca"] = "ca";
    ContentSearchResultLanguageEnum["CaAd"] = "ca-ad";
    ContentSearchResultLanguageEnum["CaEs"] = "ca-es";
    ContentSearchResultLanguageEnum["CaFr"] = "ca-fr";
    ContentSearchResultLanguageEnum["CaIt"] = "ca-it";
    ContentSearchResultLanguageEnum["Ccp"] = "ccp";
    ContentSearchResultLanguageEnum["CcpBd"] = "ccp-bd";
    ContentSearchResultLanguageEnum["CcpIn"] = "ccp-in";
    ContentSearchResultLanguageEnum["Ce"] = "ce";
    ContentSearchResultLanguageEnum["CeRu"] = "ce-ru";
    ContentSearchResultLanguageEnum["Ceb"] = "ceb";
    ContentSearchResultLanguageEnum["CebPh"] = "ceb-ph";
    ContentSearchResultLanguageEnum["Cgg"] = "cgg";
    ContentSearchResultLanguageEnum["CggUg"] = "cgg-ug";
    ContentSearchResultLanguageEnum["Chr"] = "chr";
    ContentSearchResultLanguageEnum["ChrUs"] = "chr-us";
    ContentSearchResultLanguageEnum["Ckb"] = "ckb";
    ContentSearchResultLanguageEnum["CkbIq"] = "ckb-iq";
    ContentSearchResultLanguageEnum["CkbIr"] = "ckb-ir";
    ContentSearchResultLanguageEnum["Cs"] = "cs";
    ContentSearchResultLanguageEnum["CsCz"] = "cs-cz";
    ContentSearchResultLanguageEnum["Cu"] = "cu";
    ContentSearchResultLanguageEnum["CuRu"] = "cu-ru";
    ContentSearchResultLanguageEnum["Cy"] = "cy";
    ContentSearchResultLanguageEnum["CyGb"] = "cy-gb";
    ContentSearchResultLanguageEnum["Da"] = "da";
    ContentSearchResultLanguageEnum["DaDk"] = "da-dk";
    ContentSearchResultLanguageEnum["DaGl"] = "da-gl";
    ContentSearchResultLanguageEnum["Dav"] = "dav";
    ContentSearchResultLanguageEnum["DavKe"] = "dav-ke";
    ContentSearchResultLanguageEnum["De"] = "de";
    ContentSearchResultLanguageEnum["DeAt"] = "de-at";
    ContentSearchResultLanguageEnum["DeBe"] = "de-be";
    ContentSearchResultLanguageEnum["DeCh"] = "de-ch";
    ContentSearchResultLanguageEnum["DeDe"] = "de-de";
    ContentSearchResultLanguageEnum["DeGr"] = "de-gr";
    ContentSearchResultLanguageEnum["DeIt"] = "de-it";
    ContentSearchResultLanguageEnum["DeLi"] = "de-li";
    ContentSearchResultLanguageEnum["DeLu"] = "de-lu";
    ContentSearchResultLanguageEnum["Dje"] = "dje";
    ContentSearchResultLanguageEnum["DjeNe"] = "dje-ne";
    ContentSearchResultLanguageEnum["Doi"] = "doi";
    ContentSearchResultLanguageEnum["DoiIn"] = "doi-in";
    ContentSearchResultLanguageEnum["Dsb"] = "dsb";
    ContentSearchResultLanguageEnum["DsbDe"] = "dsb-de";
    ContentSearchResultLanguageEnum["Dua"] = "dua";
    ContentSearchResultLanguageEnum["DuaCm"] = "dua-cm";
    ContentSearchResultLanguageEnum["Dyo"] = "dyo";
    ContentSearchResultLanguageEnum["DyoSn"] = "dyo-sn";
    ContentSearchResultLanguageEnum["Dz"] = "dz";
    ContentSearchResultLanguageEnum["DzBt"] = "dz-bt";
    ContentSearchResultLanguageEnum["Ebu"] = "ebu";
    ContentSearchResultLanguageEnum["EbuKe"] = "ebu-ke";
    ContentSearchResultLanguageEnum["Ee"] = "ee";
    ContentSearchResultLanguageEnum["EeGh"] = "ee-gh";
    ContentSearchResultLanguageEnum["EeTg"] = "ee-tg";
    ContentSearchResultLanguageEnum["El"] = "el";
    ContentSearchResultLanguageEnum["ElCy"] = "el-cy";
    ContentSearchResultLanguageEnum["ElGr"] = "el-gr";
    ContentSearchResultLanguageEnum["En"] = "en";
    ContentSearchResultLanguageEnum["En001"] = "en-001";
    ContentSearchResultLanguageEnum["En150"] = "en-150";
    ContentSearchResultLanguageEnum["EnAe"] = "en-ae";
    ContentSearchResultLanguageEnum["EnAg"] = "en-ag";
    ContentSearchResultLanguageEnum["EnAi"] = "en-ai";
    ContentSearchResultLanguageEnum["EnAs"] = "en-as";
    ContentSearchResultLanguageEnum["EnAt"] = "en-at";
    ContentSearchResultLanguageEnum["EnAu"] = "en-au";
    ContentSearchResultLanguageEnum["EnBb"] = "en-bb";
    ContentSearchResultLanguageEnum["EnBe"] = "en-be";
    ContentSearchResultLanguageEnum["EnBi"] = "en-bi";
    ContentSearchResultLanguageEnum["EnBm"] = "en-bm";
    ContentSearchResultLanguageEnum["EnBs"] = "en-bs";
    ContentSearchResultLanguageEnum["EnBw"] = "en-bw";
    ContentSearchResultLanguageEnum["EnBz"] = "en-bz";
    ContentSearchResultLanguageEnum["EnCa"] = "en-ca";
    ContentSearchResultLanguageEnum["EnCc"] = "en-cc";
    ContentSearchResultLanguageEnum["EnCh"] = "en-ch";
    ContentSearchResultLanguageEnum["EnCk"] = "en-ck";
    ContentSearchResultLanguageEnum["EnCm"] = "en-cm";
    ContentSearchResultLanguageEnum["EnCn"] = "en-cn";
    ContentSearchResultLanguageEnum["EnCx"] = "en-cx";
    ContentSearchResultLanguageEnum["EnCy"] = "en-cy";
    ContentSearchResultLanguageEnum["EnDe"] = "en-de";
    ContentSearchResultLanguageEnum["EnDg"] = "en-dg";
    ContentSearchResultLanguageEnum["EnDk"] = "en-dk";
    ContentSearchResultLanguageEnum["EnDm"] = "en-dm";
    ContentSearchResultLanguageEnum["EnEr"] = "en-er";
    ContentSearchResultLanguageEnum["EnFi"] = "en-fi";
    ContentSearchResultLanguageEnum["EnFj"] = "en-fj";
    ContentSearchResultLanguageEnum["EnFk"] = "en-fk";
    ContentSearchResultLanguageEnum["EnFm"] = "en-fm";
    ContentSearchResultLanguageEnum["EnGb"] = "en-gb";
    ContentSearchResultLanguageEnum["EnGd"] = "en-gd";
    ContentSearchResultLanguageEnum["EnGg"] = "en-gg";
    ContentSearchResultLanguageEnum["EnGh"] = "en-gh";
    ContentSearchResultLanguageEnum["EnGi"] = "en-gi";
    ContentSearchResultLanguageEnum["EnGm"] = "en-gm";
    ContentSearchResultLanguageEnum["EnGu"] = "en-gu";
    ContentSearchResultLanguageEnum["EnGy"] = "en-gy";
    ContentSearchResultLanguageEnum["EnHk"] = "en-hk";
    ContentSearchResultLanguageEnum["EnIe"] = "en-ie";
    ContentSearchResultLanguageEnum["EnIl"] = "en-il";
    ContentSearchResultLanguageEnum["EnIm"] = "en-im";
    ContentSearchResultLanguageEnum["EnIn"] = "en-in";
    ContentSearchResultLanguageEnum["EnIo"] = "en-io";
    ContentSearchResultLanguageEnum["EnJe"] = "en-je";
    ContentSearchResultLanguageEnum["EnJm"] = "en-jm";
    ContentSearchResultLanguageEnum["EnKe"] = "en-ke";
    ContentSearchResultLanguageEnum["EnKi"] = "en-ki";
    ContentSearchResultLanguageEnum["EnKn"] = "en-kn";
    ContentSearchResultLanguageEnum["EnKy"] = "en-ky";
    ContentSearchResultLanguageEnum["EnLc"] = "en-lc";
    ContentSearchResultLanguageEnum["EnLr"] = "en-lr";
    ContentSearchResultLanguageEnum["EnLs"] = "en-ls";
    ContentSearchResultLanguageEnum["EnLu"] = "en-lu";
    ContentSearchResultLanguageEnum["EnMg"] = "en-mg";
    ContentSearchResultLanguageEnum["EnMh"] = "en-mh";
    ContentSearchResultLanguageEnum["EnMo"] = "en-mo";
    ContentSearchResultLanguageEnum["EnMp"] = "en-mp";
    ContentSearchResultLanguageEnum["EnMs"] = "en-ms";
    ContentSearchResultLanguageEnum["EnMt"] = "en-mt";
    ContentSearchResultLanguageEnum["EnMu"] = "en-mu";
    ContentSearchResultLanguageEnum["EnMw"] = "en-mw";
    ContentSearchResultLanguageEnum["EnMx"] = "en-mx";
    ContentSearchResultLanguageEnum["EnMy"] = "en-my";
    ContentSearchResultLanguageEnum["EnNa"] = "en-na";
    ContentSearchResultLanguageEnum["EnNf"] = "en-nf";
    ContentSearchResultLanguageEnum["EnNg"] = "en-ng";
    ContentSearchResultLanguageEnum["EnNl"] = "en-nl";
    ContentSearchResultLanguageEnum["EnNr"] = "en-nr";
    ContentSearchResultLanguageEnum["EnNu"] = "en-nu";
    ContentSearchResultLanguageEnum["EnNz"] = "en-nz";
    ContentSearchResultLanguageEnum["EnPg"] = "en-pg";
    ContentSearchResultLanguageEnum["EnPh"] = "en-ph";
    ContentSearchResultLanguageEnum["EnPk"] = "en-pk";
    ContentSearchResultLanguageEnum["EnPn"] = "en-pn";
    ContentSearchResultLanguageEnum["EnPr"] = "en-pr";
    ContentSearchResultLanguageEnum["EnPw"] = "en-pw";
    ContentSearchResultLanguageEnum["EnRw"] = "en-rw";
    ContentSearchResultLanguageEnum["EnSb"] = "en-sb";
    ContentSearchResultLanguageEnum["EnSc"] = "en-sc";
    ContentSearchResultLanguageEnum["EnSd"] = "en-sd";
    ContentSearchResultLanguageEnum["EnSe"] = "en-se";
    ContentSearchResultLanguageEnum["EnSg"] = "en-sg";
    ContentSearchResultLanguageEnum["EnSh"] = "en-sh";
    ContentSearchResultLanguageEnum["EnSi"] = "en-si";
    ContentSearchResultLanguageEnum["EnSl"] = "en-sl";
    ContentSearchResultLanguageEnum["EnSs"] = "en-ss";
    ContentSearchResultLanguageEnum["EnSx"] = "en-sx";
    ContentSearchResultLanguageEnum["EnSz"] = "en-sz";
    ContentSearchResultLanguageEnum["EnTc"] = "en-tc";
    ContentSearchResultLanguageEnum["EnTk"] = "en-tk";
    ContentSearchResultLanguageEnum["EnTo"] = "en-to";
    ContentSearchResultLanguageEnum["EnTt"] = "en-tt";
    ContentSearchResultLanguageEnum["EnTv"] = "en-tv";
    ContentSearchResultLanguageEnum["EnTz"] = "en-tz";
    ContentSearchResultLanguageEnum["EnUg"] = "en-ug";
    ContentSearchResultLanguageEnum["EnUm"] = "en-um";
    ContentSearchResultLanguageEnum["EnUs"] = "en-us";
    ContentSearchResultLanguageEnum["EnVc"] = "en-vc";
    ContentSearchResultLanguageEnum["EnVg"] = "en-vg";
    ContentSearchResultLanguageEnum["EnVi"] = "en-vi";
    ContentSearchResultLanguageEnum["EnVu"] = "en-vu";
    ContentSearchResultLanguageEnum["EnWs"] = "en-ws";
    ContentSearchResultLanguageEnum["EnZa"] = "en-za";
    ContentSearchResultLanguageEnum["EnZm"] = "en-zm";
    ContentSearchResultLanguageEnum["EnZw"] = "en-zw";
    ContentSearchResultLanguageEnum["Eo"] = "eo";
    ContentSearchResultLanguageEnum["Eo001"] = "eo-001";
    ContentSearchResultLanguageEnum["Es"] = "es";
    ContentSearchResultLanguageEnum["Es419"] = "es-419";
    ContentSearchResultLanguageEnum["EsAr"] = "es-ar";
    ContentSearchResultLanguageEnum["EsBo"] = "es-bo";
    ContentSearchResultLanguageEnum["EsBr"] = "es-br";
    ContentSearchResultLanguageEnum["EsBz"] = "es-bz";
    ContentSearchResultLanguageEnum["EsCl"] = "es-cl";
    ContentSearchResultLanguageEnum["EsCo"] = "es-co";
    ContentSearchResultLanguageEnum["EsCr"] = "es-cr";
    ContentSearchResultLanguageEnum["EsCu"] = "es-cu";
    ContentSearchResultLanguageEnum["EsDo"] = "es-do";
    ContentSearchResultLanguageEnum["EsEa"] = "es-ea";
    ContentSearchResultLanguageEnum["EsEc"] = "es-ec";
    ContentSearchResultLanguageEnum["EsEs"] = "es-es";
    ContentSearchResultLanguageEnum["EsGq"] = "es-gq";
    ContentSearchResultLanguageEnum["EsGt"] = "es-gt";
    ContentSearchResultLanguageEnum["EsHn"] = "es-hn";
    ContentSearchResultLanguageEnum["EsIc"] = "es-ic";
    ContentSearchResultLanguageEnum["EsMx"] = "es-mx";
    ContentSearchResultLanguageEnum["EsNi"] = "es-ni";
    ContentSearchResultLanguageEnum["EsPa"] = "es-pa";
    ContentSearchResultLanguageEnum["EsPe"] = "es-pe";
    ContentSearchResultLanguageEnum["EsPh"] = "es-ph";
    ContentSearchResultLanguageEnum["EsPr"] = "es-pr";
    ContentSearchResultLanguageEnum["EsPy"] = "es-py";
    ContentSearchResultLanguageEnum["EsSv"] = "es-sv";
    ContentSearchResultLanguageEnum["EsUs"] = "es-us";
    ContentSearchResultLanguageEnum["EsUy"] = "es-uy";
    ContentSearchResultLanguageEnum["EsVe"] = "es-ve";
    ContentSearchResultLanguageEnum["Et"] = "et";
    ContentSearchResultLanguageEnum["EtEe"] = "et-ee";
    ContentSearchResultLanguageEnum["Eu"] = "eu";
    ContentSearchResultLanguageEnum["EuEs"] = "eu-es";
    ContentSearchResultLanguageEnum["Ewo"] = "ewo";
    ContentSearchResultLanguageEnum["EwoCm"] = "ewo-cm";
    ContentSearchResultLanguageEnum["Fa"] = "fa";
    ContentSearchResultLanguageEnum["FaAf"] = "fa-af";
    ContentSearchResultLanguageEnum["FaIr"] = "fa-ir";
    ContentSearchResultLanguageEnum["Ff"] = "ff";
    ContentSearchResultLanguageEnum["FfBf"] = "ff-bf";
    ContentSearchResultLanguageEnum["FfCm"] = "ff-cm";
    ContentSearchResultLanguageEnum["FfGh"] = "ff-gh";
    ContentSearchResultLanguageEnum["FfGm"] = "ff-gm";
    ContentSearchResultLanguageEnum["FfGn"] = "ff-gn";
    ContentSearchResultLanguageEnum["FfGw"] = "ff-gw";
    ContentSearchResultLanguageEnum["FfLr"] = "ff-lr";
    ContentSearchResultLanguageEnum["FfMr"] = "ff-mr";
    ContentSearchResultLanguageEnum["FfNe"] = "ff-ne";
    ContentSearchResultLanguageEnum["FfNg"] = "ff-ng";
    ContentSearchResultLanguageEnum["FfSl"] = "ff-sl";
    ContentSearchResultLanguageEnum["FfSn"] = "ff-sn";
    ContentSearchResultLanguageEnum["Fi"] = "fi";
    ContentSearchResultLanguageEnum["FiFi"] = "fi-fi";
    ContentSearchResultLanguageEnum["Fil"] = "fil";
    ContentSearchResultLanguageEnum["FilPh"] = "fil-ph";
    ContentSearchResultLanguageEnum["Fo"] = "fo";
    ContentSearchResultLanguageEnum["FoDk"] = "fo-dk";
    ContentSearchResultLanguageEnum["FoFo"] = "fo-fo";
    ContentSearchResultLanguageEnum["Fr"] = "fr";
    ContentSearchResultLanguageEnum["FrBe"] = "fr-be";
    ContentSearchResultLanguageEnum["FrBf"] = "fr-bf";
    ContentSearchResultLanguageEnum["FrBi"] = "fr-bi";
    ContentSearchResultLanguageEnum["FrBj"] = "fr-bj";
    ContentSearchResultLanguageEnum["FrBl"] = "fr-bl";
    ContentSearchResultLanguageEnum["FrCa"] = "fr-ca";
    ContentSearchResultLanguageEnum["FrCd"] = "fr-cd";
    ContentSearchResultLanguageEnum["FrCf"] = "fr-cf";
    ContentSearchResultLanguageEnum["FrCg"] = "fr-cg";
    ContentSearchResultLanguageEnum["FrCh"] = "fr-ch";
    ContentSearchResultLanguageEnum["FrCi"] = "fr-ci";
    ContentSearchResultLanguageEnum["FrCm"] = "fr-cm";
    ContentSearchResultLanguageEnum["FrDj"] = "fr-dj";
    ContentSearchResultLanguageEnum["FrDz"] = "fr-dz";
    ContentSearchResultLanguageEnum["FrFr"] = "fr-fr";
    ContentSearchResultLanguageEnum["FrGa"] = "fr-ga";
    ContentSearchResultLanguageEnum["FrGf"] = "fr-gf";
    ContentSearchResultLanguageEnum["FrGn"] = "fr-gn";
    ContentSearchResultLanguageEnum["FrGp"] = "fr-gp";
    ContentSearchResultLanguageEnum["FrGq"] = "fr-gq";
    ContentSearchResultLanguageEnum["FrHt"] = "fr-ht";
    ContentSearchResultLanguageEnum["FrKm"] = "fr-km";
    ContentSearchResultLanguageEnum["FrLu"] = "fr-lu";
    ContentSearchResultLanguageEnum["FrMa"] = "fr-ma";
    ContentSearchResultLanguageEnum["FrMc"] = "fr-mc";
    ContentSearchResultLanguageEnum["FrMf"] = "fr-mf";
    ContentSearchResultLanguageEnum["FrMg"] = "fr-mg";
    ContentSearchResultLanguageEnum["FrMl"] = "fr-ml";
    ContentSearchResultLanguageEnum["FrMq"] = "fr-mq";
    ContentSearchResultLanguageEnum["FrMr"] = "fr-mr";
    ContentSearchResultLanguageEnum["FrMu"] = "fr-mu";
    ContentSearchResultLanguageEnum["FrNc"] = "fr-nc";
    ContentSearchResultLanguageEnum["FrNe"] = "fr-ne";
    ContentSearchResultLanguageEnum["FrPf"] = "fr-pf";
    ContentSearchResultLanguageEnum["FrPm"] = "fr-pm";
    ContentSearchResultLanguageEnum["FrRe"] = "fr-re";
    ContentSearchResultLanguageEnum["FrRw"] = "fr-rw";
    ContentSearchResultLanguageEnum["FrSc"] = "fr-sc";
    ContentSearchResultLanguageEnum["FrSn"] = "fr-sn";
    ContentSearchResultLanguageEnum["FrSy"] = "fr-sy";
    ContentSearchResultLanguageEnum["FrTd"] = "fr-td";
    ContentSearchResultLanguageEnum["FrTg"] = "fr-tg";
    ContentSearchResultLanguageEnum["FrTn"] = "fr-tn";
    ContentSearchResultLanguageEnum["FrVu"] = "fr-vu";
    ContentSearchResultLanguageEnum["FrWf"] = "fr-wf";
    ContentSearchResultLanguageEnum["FrYt"] = "fr-yt";
    ContentSearchResultLanguageEnum["Fur"] = "fur";
    ContentSearchResultLanguageEnum["FurIt"] = "fur-it";
    ContentSearchResultLanguageEnum["Fy"] = "fy";
    ContentSearchResultLanguageEnum["FyNl"] = "fy-nl";
    ContentSearchResultLanguageEnum["Ga"] = "ga";
    ContentSearchResultLanguageEnum["GaGb"] = "ga-gb";
    ContentSearchResultLanguageEnum["GaIe"] = "ga-ie";
    ContentSearchResultLanguageEnum["Gd"] = "gd";
    ContentSearchResultLanguageEnum["GdGb"] = "gd-gb";
    ContentSearchResultLanguageEnum["Gl"] = "gl";
    ContentSearchResultLanguageEnum["GlEs"] = "gl-es";
    ContentSearchResultLanguageEnum["Gsw"] = "gsw";
    ContentSearchResultLanguageEnum["GswCh"] = "gsw-ch";
    ContentSearchResultLanguageEnum["GswFr"] = "gsw-fr";
    ContentSearchResultLanguageEnum["GswLi"] = "gsw-li";
    ContentSearchResultLanguageEnum["Gu"] = "gu";
    ContentSearchResultLanguageEnum["GuIn"] = "gu-in";
    ContentSearchResultLanguageEnum["Guz"] = "guz";
    ContentSearchResultLanguageEnum["GuzKe"] = "guz-ke";
    ContentSearchResultLanguageEnum["Gv"] = "gv";
    ContentSearchResultLanguageEnum["GvIm"] = "gv-im";
    ContentSearchResultLanguageEnum["Ha"] = "ha";
    ContentSearchResultLanguageEnum["HaGh"] = "ha-gh";
    ContentSearchResultLanguageEnum["HaNe"] = "ha-ne";
    ContentSearchResultLanguageEnum["HaNg"] = "ha-ng";
    ContentSearchResultLanguageEnum["Haw"] = "haw";
    ContentSearchResultLanguageEnum["HawUs"] = "haw-us";
    ContentSearchResultLanguageEnum["He"] = "he";
    ContentSearchResultLanguageEnum["Hi"] = "hi";
    ContentSearchResultLanguageEnum["HiIn"] = "hi-in";
    ContentSearchResultLanguageEnum["Hr"] = "hr";
    ContentSearchResultLanguageEnum["HrBa"] = "hr-ba";
    ContentSearchResultLanguageEnum["HrHr"] = "hr-hr";
    ContentSearchResultLanguageEnum["Hsb"] = "hsb";
    ContentSearchResultLanguageEnum["HsbDe"] = "hsb-de";
    ContentSearchResultLanguageEnum["Hu"] = "hu";
    ContentSearchResultLanguageEnum["HuHu"] = "hu-hu";
    ContentSearchResultLanguageEnum["Hy"] = "hy";
    ContentSearchResultLanguageEnum["HyAm"] = "hy-am";
    ContentSearchResultLanguageEnum["Ia"] = "ia";
    ContentSearchResultLanguageEnum["Ia001"] = "ia-001";
    ContentSearchResultLanguageEnum["Id"] = "id";
    ContentSearchResultLanguageEnum["Ig"] = "ig";
    ContentSearchResultLanguageEnum["IgNg"] = "ig-ng";
    ContentSearchResultLanguageEnum["Ii"] = "ii";
    ContentSearchResultLanguageEnum["IiCn"] = "ii-cn";
    ContentSearchResultLanguageEnum["IdId"] = "id-id";
    ContentSearchResultLanguageEnum["Is"] = "is";
    ContentSearchResultLanguageEnum["IsIs"] = "is-is";
    ContentSearchResultLanguageEnum["It"] = "it";
    ContentSearchResultLanguageEnum["ItCh"] = "it-ch";
    ContentSearchResultLanguageEnum["ItIt"] = "it-it";
    ContentSearchResultLanguageEnum["ItSm"] = "it-sm";
    ContentSearchResultLanguageEnum["ItVa"] = "it-va";
    ContentSearchResultLanguageEnum["HeIl"] = "he-il";
    ContentSearchResultLanguageEnum["Ja"] = "ja";
    ContentSearchResultLanguageEnum["JaJp"] = "ja-jp";
    ContentSearchResultLanguageEnum["Jgo"] = "jgo";
    ContentSearchResultLanguageEnum["JgoCm"] = "jgo-cm";
    ContentSearchResultLanguageEnum["Yi"] = "yi";
    ContentSearchResultLanguageEnum["Yi001"] = "yi-001";
    ContentSearchResultLanguageEnum["Jmc"] = "jmc";
    ContentSearchResultLanguageEnum["JmcTz"] = "jmc-tz";
    ContentSearchResultLanguageEnum["Jv"] = "jv";
    ContentSearchResultLanguageEnum["JvId"] = "jv-id";
    ContentSearchResultLanguageEnum["Ka"] = "ka";
    ContentSearchResultLanguageEnum["KaGe"] = "ka-ge";
    ContentSearchResultLanguageEnum["Kab"] = "kab";
    ContentSearchResultLanguageEnum["KabDz"] = "kab-dz";
    ContentSearchResultLanguageEnum["Kam"] = "kam";
    ContentSearchResultLanguageEnum["KamKe"] = "kam-ke";
    ContentSearchResultLanguageEnum["Kde"] = "kde";
    ContentSearchResultLanguageEnum["KdeTz"] = "kde-tz";
    ContentSearchResultLanguageEnum["Kea"] = "kea";
    ContentSearchResultLanguageEnum["KeaCv"] = "kea-cv";
    ContentSearchResultLanguageEnum["Khq"] = "khq";
    ContentSearchResultLanguageEnum["KhqMl"] = "khq-ml";
    ContentSearchResultLanguageEnum["Ki"] = "ki";
    ContentSearchResultLanguageEnum["KiKe"] = "ki-ke";
    ContentSearchResultLanguageEnum["Kk"] = "kk";
    ContentSearchResultLanguageEnum["KkKz"] = "kk-kz";
    ContentSearchResultLanguageEnum["Kkj"] = "kkj";
    ContentSearchResultLanguageEnum["KkjCm"] = "kkj-cm";
    ContentSearchResultLanguageEnum["Kl"] = "kl";
    ContentSearchResultLanguageEnum["KlGl"] = "kl-gl";
    ContentSearchResultLanguageEnum["Kln"] = "kln";
    ContentSearchResultLanguageEnum["KlnKe"] = "kln-ke";
    ContentSearchResultLanguageEnum["Km"] = "km";
    ContentSearchResultLanguageEnum["KmKh"] = "km-kh";
    ContentSearchResultLanguageEnum["Kn"] = "kn";
    ContentSearchResultLanguageEnum["KnIn"] = "kn-in";
    ContentSearchResultLanguageEnum["Ko"] = "ko";
    ContentSearchResultLanguageEnum["KoKp"] = "ko-kp";
    ContentSearchResultLanguageEnum["KoKr"] = "ko-kr";
    ContentSearchResultLanguageEnum["Kok"] = "kok";
    ContentSearchResultLanguageEnum["KokIn"] = "kok-in";
    ContentSearchResultLanguageEnum["Ks"] = "ks";
    ContentSearchResultLanguageEnum["KsIn"] = "ks-in";
    ContentSearchResultLanguageEnum["Ksb"] = "ksb";
    ContentSearchResultLanguageEnum["KsbTz"] = "ksb-tz";
    ContentSearchResultLanguageEnum["Ksf"] = "ksf";
    ContentSearchResultLanguageEnum["KsfCm"] = "ksf-cm";
    ContentSearchResultLanguageEnum["Ksh"] = "ksh";
    ContentSearchResultLanguageEnum["KshDe"] = "ksh-de";
    ContentSearchResultLanguageEnum["Kw"] = "kw";
    ContentSearchResultLanguageEnum["KwGb"] = "kw-gb";
    ContentSearchResultLanguageEnum["Ku"] = "ku";
    ContentSearchResultLanguageEnum["KuTr"] = "ku-tr";
    ContentSearchResultLanguageEnum["Ky"] = "ky";
    ContentSearchResultLanguageEnum["KyKg"] = "ky-kg";
    ContentSearchResultLanguageEnum["Lag"] = "lag";
    ContentSearchResultLanguageEnum["LagTz"] = "lag-tz";
    ContentSearchResultLanguageEnum["Lb"] = "lb";
    ContentSearchResultLanguageEnum["LbLu"] = "lb-lu";
    ContentSearchResultLanguageEnum["Lg"] = "lg";
    ContentSearchResultLanguageEnum["LgUg"] = "lg-ug";
    ContentSearchResultLanguageEnum["Lkt"] = "lkt";
    ContentSearchResultLanguageEnum["LktUs"] = "lkt-us";
    ContentSearchResultLanguageEnum["Ln"] = "ln";
    ContentSearchResultLanguageEnum["LnAo"] = "ln-ao";
    ContentSearchResultLanguageEnum["LnCd"] = "ln-cd";
    ContentSearchResultLanguageEnum["LnCf"] = "ln-cf";
    ContentSearchResultLanguageEnum["LnCg"] = "ln-cg";
    ContentSearchResultLanguageEnum["Lo"] = "lo";
    ContentSearchResultLanguageEnum["LoLa"] = "lo-la";
    ContentSearchResultLanguageEnum["Lrc"] = "lrc";
    ContentSearchResultLanguageEnum["LrcIq"] = "lrc-iq";
    ContentSearchResultLanguageEnum["LrcIr"] = "lrc-ir";
    ContentSearchResultLanguageEnum["Lt"] = "lt";
    ContentSearchResultLanguageEnum["LtLt"] = "lt-lt";
    ContentSearchResultLanguageEnum["Lu"] = "lu";
    ContentSearchResultLanguageEnum["LuCd"] = "lu-cd";
    ContentSearchResultLanguageEnum["Luo"] = "luo";
    ContentSearchResultLanguageEnum["LuoKe"] = "luo-ke";
    ContentSearchResultLanguageEnum["Luy"] = "luy";
    ContentSearchResultLanguageEnum["LuyKe"] = "luy-ke";
    ContentSearchResultLanguageEnum["Lv"] = "lv";
    ContentSearchResultLanguageEnum["LvLv"] = "lv-lv";
    ContentSearchResultLanguageEnum["Mai"] = "mai";
    ContentSearchResultLanguageEnum["MaiIn"] = "mai-in";
    ContentSearchResultLanguageEnum["Mas"] = "mas";
    ContentSearchResultLanguageEnum["MasKe"] = "mas-ke";
    ContentSearchResultLanguageEnum["MasTz"] = "mas-tz";
    ContentSearchResultLanguageEnum["Mer"] = "mer";
    ContentSearchResultLanguageEnum["MerKe"] = "mer-ke";
    ContentSearchResultLanguageEnum["Mfe"] = "mfe";
    ContentSearchResultLanguageEnum["MfeMu"] = "mfe-mu";
    ContentSearchResultLanguageEnum["Mg"] = "mg";
    ContentSearchResultLanguageEnum["MgMg"] = "mg-mg";
    ContentSearchResultLanguageEnum["Mgh"] = "mgh";
    ContentSearchResultLanguageEnum["MghMz"] = "mgh-mz";
    ContentSearchResultLanguageEnum["Mgo"] = "mgo";
    ContentSearchResultLanguageEnum["MgoCm"] = "mgo-cm";
    ContentSearchResultLanguageEnum["Mi"] = "mi";
    ContentSearchResultLanguageEnum["MiNz"] = "mi-nz";
    ContentSearchResultLanguageEnum["Mk"] = "mk";
    ContentSearchResultLanguageEnum["MkMk"] = "mk-mk";
    ContentSearchResultLanguageEnum["Ml"] = "ml";
    ContentSearchResultLanguageEnum["MlIn"] = "ml-in";
    ContentSearchResultLanguageEnum["Mn"] = "mn";
    ContentSearchResultLanguageEnum["MnMn"] = "mn-mn";
    ContentSearchResultLanguageEnum["Mni"] = "mni";
    ContentSearchResultLanguageEnum["MniIn"] = "mni-in";
    ContentSearchResultLanguageEnum["Mr"] = "mr";
    ContentSearchResultLanguageEnum["MrIn"] = "mr-in";
    ContentSearchResultLanguageEnum["Ms"] = "ms";
    ContentSearchResultLanguageEnum["MsBn"] = "ms-bn";
    ContentSearchResultLanguageEnum["MsId"] = "ms-id";
    ContentSearchResultLanguageEnum["MsMy"] = "ms-my";
    ContentSearchResultLanguageEnum["MsSg"] = "ms-sg";
    ContentSearchResultLanguageEnum["Mt"] = "mt";
    ContentSearchResultLanguageEnum["MtMt"] = "mt-mt";
    ContentSearchResultLanguageEnum["Mua"] = "mua";
    ContentSearchResultLanguageEnum["MuaCm"] = "mua-cm";
    ContentSearchResultLanguageEnum["My"] = "my";
    ContentSearchResultLanguageEnum["MyMm"] = "my-mm";
    ContentSearchResultLanguageEnum["Mzn"] = "mzn";
    ContentSearchResultLanguageEnum["MznIr"] = "mzn-ir";
    ContentSearchResultLanguageEnum["Naq"] = "naq";
    ContentSearchResultLanguageEnum["NaqNa"] = "naq-na";
    ContentSearchResultLanguageEnum["Nb"] = "nb";
    ContentSearchResultLanguageEnum["NbNo"] = "nb-no";
    ContentSearchResultLanguageEnum["NbSj"] = "nb-sj";
    ContentSearchResultLanguageEnum["Nd"] = "nd";
    ContentSearchResultLanguageEnum["NdZw"] = "nd-zw";
    ContentSearchResultLanguageEnum["Nds"] = "nds";
    ContentSearchResultLanguageEnum["NdsDe"] = "nds-de";
    ContentSearchResultLanguageEnum["NdsNl"] = "nds-nl";
    ContentSearchResultLanguageEnum["Ne"] = "ne";
    ContentSearchResultLanguageEnum["NeIn"] = "ne-in";
    ContentSearchResultLanguageEnum["NeNp"] = "ne-np";
    ContentSearchResultLanguageEnum["Nl"] = "nl";
    ContentSearchResultLanguageEnum["NlAw"] = "nl-aw";
    ContentSearchResultLanguageEnum["NlBe"] = "nl-be";
    ContentSearchResultLanguageEnum["NlCh"] = "nl-ch";
    ContentSearchResultLanguageEnum["NlBq"] = "nl-bq";
    ContentSearchResultLanguageEnum["NlCw"] = "nl-cw";
    ContentSearchResultLanguageEnum["NlLu"] = "nl-lu";
    ContentSearchResultLanguageEnum["NlNl"] = "nl-nl";
    ContentSearchResultLanguageEnum["NlSr"] = "nl-sr";
    ContentSearchResultLanguageEnum["NlSx"] = "nl-sx";
    ContentSearchResultLanguageEnum["Nmg"] = "nmg";
    ContentSearchResultLanguageEnum["NmgCm"] = "nmg-cm";
    ContentSearchResultLanguageEnum["Nn"] = "nn";
    ContentSearchResultLanguageEnum["NnNo"] = "nn-no";
    ContentSearchResultLanguageEnum["Nnh"] = "nnh";
    ContentSearchResultLanguageEnum["NnhCm"] = "nnh-cm";
    ContentSearchResultLanguageEnum["No"] = "no";
    ContentSearchResultLanguageEnum["NoNo"] = "no-no";
    ContentSearchResultLanguageEnum["Nus"] = "nus";
    ContentSearchResultLanguageEnum["NusSs"] = "nus-ss";
    ContentSearchResultLanguageEnum["Nyn"] = "nyn";
    ContentSearchResultLanguageEnum["NynUg"] = "nyn-ug";
    ContentSearchResultLanguageEnum["Om"] = "om";
    ContentSearchResultLanguageEnum["OmEt"] = "om-et";
    ContentSearchResultLanguageEnum["OmKe"] = "om-ke";
    ContentSearchResultLanguageEnum["Or"] = "or";
    ContentSearchResultLanguageEnum["OrIn"] = "or-in";
    ContentSearchResultLanguageEnum["Os"] = "os";
    ContentSearchResultLanguageEnum["OsGe"] = "os-ge";
    ContentSearchResultLanguageEnum["OsRu"] = "os-ru";
    ContentSearchResultLanguageEnum["Pa"] = "pa";
    ContentSearchResultLanguageEnum["PaIn"] = "pa-in";
    ContentSearchResultLanguageEnum["PaPk"] = "pa-pk";
    ContentSearchResultLanguageEnum["Pcm"] = "pcm";
    ContentSearchResultLanguageEnum["PcmNg"] = "pcm-ng";
    ContentSearchResultLanguageEnum["Pl"] = "pl";
    ContentSearchResultLanguageEnum["PlPl"] = "pl-pl";
    ContentSearchResultLanguageEnum["Prg"] = "prg";
    ContentSearchResultLanguageEnum["Prg001"] = "prg-001";
    ContentSearchResultLanguageEnum["Ps"] = "ps";
    ContentSearchResultLanguageEnum["PsAf"] = "ps-af";
    ContentSearchResultLanguageEnum["PsPk"] = "ps-pk";
    ContentSearchResultLanguageEnum["Pt"] = "pt";
    ContentSearchResultLanguageEnum["PtAo"] = "pt-ao";
    ContentSearchResultLanguageEnum["PtBr"] = "pt-br";
    ContentSearchResultLanguageEnum["PtCh"] = "pt-ch";
    ContentSearchResultLanguageEnum["PtCv"] = "pt-cv";
    ContentSearchResultLanguageEnum["PtGq"] = "pt-gq";
    ContentSearchResultLanguageEnum["PtGw"] = "pt-gw";
    ContentSearchResultLanguageEnum["PtLu"] = "pt-lu";
    ContentSearchResultLanguageEnum["PtMo"] = "pt-mo";
    ContentSearchResultLanguageEnum["PtMz"] = "pt-mz";
    ContentSearchResultLanguageEnum["PtPt"] = "pt-pt";
    ContentSearchResultLanguageEnum["PtSt"] = "pt-st";
    ContentSearchResultLanguageEnum["PtTl"] = "pt-tl";
    ContentSearchResultLanguageEnum["Qu"] = "qu";
    ContentSearchResultLanguageEnum["QuBo"] = "qu-bo";
    ContentSearchResultLanguageEnum["QuEc"] = "qu-ec";
    ContentSearchResultLanguageEnum["QuPe"] = "qu-pe";
    ContentSearchResultLanguageEnum["Rm"] = "rm";
    ContentSearchResultLanguageEnum["RmCh"] = "rm-ch";
    ContentSearchResultLanguageEnum["Rn"] = "rn";
    ContentSearchResultLanguageEnum["RnBi"] = "rn-bi";
    ContentSearchResultLanguageEnum["Ro"] = "ro";
    ContentSearchResultLanguageEnum["RoMd"] = "ro-md";
    ContentSearchResultLanguageEnum["RoRo"] = "ro-ro";
    ContentSearchResultLanguageEnum["Rof"] = "rof";
    ContentSearchResultLanguageEnum["RofTz"] = "rof-tz";
    ContentSearchResultLanguageEnum["Ru"] = "ru";
    ContentSearchResultLanguageEnum["RuBy"] = "ru-by";
    ContentSearchResultLanguageEnum["RuKg"] = "ru-kg";
    ContentSearchResultLanguageEnum["RuKz"] = "ru-kz";
    ContentSearchResultLanguageEnum["RuMd"] = "ru-md";
    ContentSearchResultLanguageEnum["RuRu"] = "ru-ru";
    ContentSearchResultLanguageEnum["RuUa"] = "ru-ua";
    ContentSearchResultLanguageEnum["Rw"] = "rw";
    ContentSearchResultLanguageEnum["RwRw"] = "rw-rw";
    ContentSearchResultLanguageEnum["Rwk"] = "rwk";
    ContentSearchResultLanguageEnum["RwkTz"] = "rwk-tz";
    ContentSearchResultLanguageEnum["Sa"] = "sa";
    ContentSearchResultLanguageEnum["SaIn"] = "sa-in";
    ContentSearchResultLanguageEnum["Sah"] = "sah";
    ContentSearchResultLanguageEnum["SahRu"] = "sah-ru";
    ContentSearchResultLanguageEnum["Saq"] = "saq";
    ContentSearchResultLanguageEnum["SaqKe"] = "saq-ke";
    ContentSearchResultLanguageEnum["Sat"] = "sat";
    ContentSearchResultLanguageEnum["SatIn"] = "sat-in";
    ContentSearchResultLanguageEnum["Sbp"] = "sbp";
    ContentSearchResultLanguageEnum["SbpTz"] = "sbp-tz";
    ContentSearchResultLanguageEnum["Sd"] = "sd";
    ContentSearchResultLanguageEnum["SdIn"] = "sd-in";
    ContentSearchResultLanguageEnum["SdPk"] = "sd-pk";
    ContentSearchResultLanguageEnum["Se"] = "se";
    ContentSearchResultLanguageEnum["SeFi"] = "se-fi";
    ContentSearchResultLanguageEnum["SeNo"] = "se-no";
    ContentSearchResultLanguageEnum["SeSe"] = "se-se";
    ContentSearchResultLanguageEnum["Seh"] = "seh";
    ContentSearchResultLanguageEnum["SehMz"] = "seh-mz";
    ContentSearchResultLanguageEnum["Ses"] = "ses";
    ContentSearchResultLanguageEnum["SesMl"] = "ses-ml";
    ContentSearchResultLanguageEnum["Sg"] = "sg";
    ContentSearchResultLanguageEnum["SgCf"] = "sg-cf";
    ContentSearchResultLanguageEnum["Shi"] = "shi";
    ContentSearchResultLanguageEnum["ShiMa"] = "shi-ma";
    ContentSearchResultLanguageEnum["Si"] = "si";
    ContentSearchResultLanguageEnum["SiLk"] = "si-lk";
    ContentSearchResultLanguageEnum["Sk"] = "sk";
    ContentSearchResultLanguageEnum["SkSk"] = "sk-sk";
    ContentSearchResultLanguageEnum["Sl"] = "sl";
    ContentSearchResultLanguageEnum["SlSi"] = "sl-si";
    ContentSearchResultLanguageEnum["Smn"] = "smn";
    ContentSearchResultLanguageEnum["SmnFi"] = "smn-fi";
    ContentSearchResultLanguageEnum["Sn"] = "sn";
    ContentSearchResultLanguageEnum["SnZw"] = "sn-zw";
    ContentSearchResultLanguageEnum["So"] = "so";
    ContentSearchResultLanguageEnum["SoDj"] = "so-dj";
    ContentSearchResultLanguageEnum["SoEt"] = "so-et";
    ContentSearchResultLanguageEnum["SoKe"] = "so-ke";
    ContentSearchResultLanguageEnum["SoSo"] = "so-so";
    ContentSearchResultLanguageEnum["Sq"] = "sq";
    ContentSearchResultLanguageEnum["SqAl"] = "sq-al";
    ContentSearchResultLanguageEnum["SqMk"] = "sq-mk";
    ContentSearchResultLanguageEnum["SqXk"] = "sq-xk";
    ContentSearchResultLanguageEnum["Sr"] = "sr";
    ContentSearchResultLanguageEnum["SrBa"] = "sr-ba";
    ContentSearchResultLanguageEnum["SrCs"] = "sr-cs";
    ContentSearchResultLanguageEnum["SrMe"] = "sr-me";
    ContentSearchResultLanguageEnum["SrRs"] = "sr-rs";
    ContentSearchResultLanguageEnum["SrXk"] = "sr-xk";
    ContentSearchResultLanguageEnum["Su"] = "su";
    ContentSearchResultLanguageEnum["SuId"] = "su-id";
    ContentSearchResultLanguageEnum["Sv"] = "sv";
    ContentSearchResultLanguageEnum["SvAx"] = "sv-ax";
    ContentSearchResultLanguageEnum["SvFi"] = "sv-fi";
    ContentSearchResultLanguageEnum["SvSe"] = "sv-se";
    ContentSearchResultLanguageEnum["Sw"] = "sw";
    ContentSearchResultLanguageEnum["SwCd"] = "sw-cd";
    ContentSearchResultLanguageEnum["SwKe"] = "sw-ke";
    ContentSearchResultLanguageEnum["SwTz"] = "sw-tz";
    ContentSearchResultLanguageEnum["SwUg"] = "sw-ug";
    ContentSearchResultLanguageEnum["Sy"] = "sy";
    ContentSearchResultLanguageEnum["Ta"] = "ta";
    ContentSearchResultLanguageEnum["TaIn"] = "ta-in";
    ContentSearchResultLanguageEnum["TaLk"] = "ta-lk";
    ContentSearchResultLanguageEnum["TaMy"] = "ta-my";
    ContentSearchResultLanguageEnum["TaSg"] = "ta-sg";
    ContentSearchResultLanguageEnum["Te"] = "te";
    ContentSearchResultLanguageEnum["TeIn"] = "te-in";
    ContentSearchResultLanguageEnum["Teo"] = "teo";
    ContentSearchResultLanguageEnum["TeoKe"] = "teo-ke";
    ContentSearchResultLanguageEnum["TeoUg"] = "teo-ug";
    ContentSearchResultLanguageEnum["Tg"] = "tg";
    ContentSearchResultLanguageEnum["TgTj"] = "tg-tj";
    ContentSearchResultLanguageEnum["Th"] = "th";
    ContentSearchResultLanguageEnum["ThTh"] = "th-th";
    ContentSearchResultLanguageEnum["Ti"] = "ti";
    ContentSearchResultLanguageEnum["TiEr"] = "ti-er";
    ContentSearchResultLanguageEnum["TiEt"] = "ti-et";
    ContentSearchResultLanguageEnum["Tk"] = "tk";
    ContentSearchResultLanguageEnum["TkTm"] = "tk-tm";
    ContentSearchResultLanguageEnum["Tl"] = "tl";
    ContentSearchResultLanguageEnum["To"] = "to";
    ContentSearchResultLanguageEnum["ToTo"] = "to-to";
    ContentSearchResultLanguageEnum["Tr"] = "tr";
    ContentSearchResultLanguageEnum["TrCy"] = "tr-cy";
    ContentSearchResultLanguageEnum["TrTr"] = "tr-tr";
    ContentSearchResultLanguageEnum["Tt"] = "tt";
    ContentSearchResultLanguageEnum["TtRu"] = "tt-ru";
    ContentSearchResultLanguageEnum["Twq"] = "twq";
    ContentSearchResultLanguageEnum["TwqNe"] = "twq-ne";
    ContentSearchResultLanguageEnum["Tzm"] = "tzm";
    ContentSearchResultLanguageEnum["TzmMa"] = "tzm-ma";
    ContentSearchResultLanguageEnum["Ug"] = "ug";
    ContentSearchResultLanguageEnum["UgCn"] = "ug-cn";
    ContentSearchResultLanguageEnum["Uk"] = "uk";
    ContentSearchResultLanguageEnum["UkUa"] = "uk-ua";
    ContentSearchResultLanguageEnum["Ur"] = "ur";
    ContentSearchResultLanguageEnum["UrIn"] = "ur-in";
    ContentSearchResultLanguageEnum["UrPk"] = "ur-pk";
    ContentSearchResultLanguageEnum["Uz"] = "uz";
    ContentSearchResultLanguageEnum["UzAf"] = "uz-af";
    ContentSearchResultLanguageEnum["UzUz"] = "uz-uz";
    ContentSearchResultLanguageEnum["Vai"] = "vai";
    ContentSearchResultLanguageEnum["VaiLr"] = "vai-lr";
    ContentSearchResultLanguageEnum["Vi"] = "vi";
    ContentSearchResultLanguageEnum["ViVn"] = "vi-vn";
    ContentSearchResultLanguageEnum["Vo"] = "vo";
    ContentSearchResultLanguageEnum["Vo001"] = "vo-001";
    ContentSearchResultLanguageEnum["Vun"] = "vun";
    ContentSearchResultLanguageEnum["VunTz"] = "vun-tz";
    ContentSearchResultLanguageEnum["Wae"] = "wae";
    ContentSearchResultLanguageEnum["WaeCh"] = "wae-ch";
    ContentSearchResultLanguageEnum["Wo"] = "wo";
    ContentSearchResultLanguageEnum["WoSn"] = "wo-sn";
    ContentSearchResultLanguageEnum["Xh"] = "xh";
    ContentSearchResultLanguageEnum["XhZa"] = "xh-za";
    ContentSearchResultLanguageEnum["Xog"] = "xog";
    ContentSearchResultLanguageEnum["XogUg"] = "xog-ug";
    ContentSearchResultLanguageEnum["Yav"] = "yav";
    ContentSearchResultLanguageEnum["YavCm"] = "yav-cm";
    ContentSearchResultLanguageEnum["Yo"] = "yo";
    ContentSearchResultLanguageEnum["YoBj"] = "yo-bj";
    ContentSearchResultLanguageEnum["YoNg"] = "yo-ng";
    ContentSearchResultLanguageEnum["Yue"] = "yue";
    ContentSearchResultLanguageEnum["YueCn"] = "yue-cn";
    ContentSearchResultLanguageEnum["YueHk"] = "yue-hk";
    ContentSearchResultLanguageEnum["Zgh"] = "zgh";
    ContentSearchResultLanguageEnum["ZghMa"] = "zgh-ma";
    ContentSearchResultLanguageEnum["Zh"] = "zh";
    ContentSearchResultLanguageEnum["ZhCn"] = "zh-cn";
    ContentSearchResultLanguageEnum["ZhHk"] = "zh-hk";
    ContentSearchResultLanguageEnum["ZhMo"] = "zh-mo";
    ContentSearchResultLanguageEnum["ZhSg"] = "zh-sg";
    ContentSearchResultLanguageEnum["ZhTw"] = "zh-tw";
    ContentSearchResultLanguageEnum["ZhHans"] = "zh-hans";
    ContentSearchResultLanguageEnum["ZhHant"] = "zh-hant";
    ContentSearchResultLanguageEnum["Zu"] = "zu";
    ContentSearchResultLanguageEnum["ZuZa"] = "zu-za";
})(ContentSearchResultLanguageEnum = exports.ContentSearchResultLanguageEnum || (exports.ContentSearchResultLanguageEnum = {}));
var ContentSearchResultTypeEnum;
(function (ContentSearchResultTypeEnum) {
    ContentSearchResultTypeEnum["LandingPage"] = "LANDING_PAGE";
    ContentSearchResultTypeEnum["BlogPost"] = "BLOG_POST";
    ContentSearchResultTypeEnum["SitePage"] = "SITE_PAGE";
    ContentSearchResultTypeEnum["KnowledgeArticle"] = "KNOWLEDGE_ARTICLE";
    ContentSearchResultTypeEnum["ListingPage"] = "LISTING_PAGE";
})(ContentSearchResultTypeEnum = exports.ContentSearchResultTypeEnum || (exports.ContentSearchResultTypeEnum = {}));
//# sourceMappingURL=ContentSearchResult.js.map