{"version": 3, "file": "ObservableAPI.js", "sourceRoot": "", "sources": ["../../../../../codegen/cms/performance/types/ObservableAPI.ts"], "names": [], "mappings": ";;;AAEA,0CAAmD;AACnD,0CAA2C;AAG3C,uEAAwH;AACxH,MAAa,8BAA8B;IAKvC,YACI,aAA4B,EAC5B,cAAmD,EACnD,iBAAyD;QAEzD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,IAAI,yDAAkC,CAAC,aAAa,CAAC,CAAC;QAC9F,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,IAAI,IAAI,4DAAqC,EAAE,CAAC;IAC9F,CAAC;IAcM,mBAAmB,CAAC,MAAe,EAAE,IAAa,EAAE,GAAa,EAAE,GAAa,EAAE,MAAe,EAAE,QAAiB,EAAE,KAAc,EAAE,GAAY,EAAE,QAAwB;QAC/K,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QAG1H,IAAI,uBAAuB,GAAG,IAAA,eAAI,EAAiB,qBAAqB,CAAC,CAAC;QAC1E,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;YAClD,uBAAuB,GAAG,uBAAuB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAClH;QAED,OAAO,uBAAuB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACxG,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,QAAyB,EAAE,EAAE;YACxC,IAAI,wBAAwB,GAAG,IAAA,aAAE,EAAC,QAAQ,CAAC,CAAC;YAC5C,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAClD,wBAAwB,GAAG,wBAAwB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAoB,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACtH;YACD,OAAO,wBAAwB,CAAC,IAAI,CAAC,IAAA,cAAG,EAAC,CAAC,GAAoB,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACzH,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;IAcM,OAAO,CAAC,MAAe,EAAE,IAAa,EAAE,GAAa,EAAE,GAAa,EAAE,MAAe,EAAE,QAAiB,EAAE,KAAc,EAAE,GAAY,EAAE,QAAwB;QACnK,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAA,cAAG,EAAC,CAAC,WAAgD,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACtL,CAAC;IAcM,qBAAqB,CAAC,MAAe,EAAE,IAAa,EAAE,GAAa,EAAE,GAAa,EAAE,MAAe,EAAE,QAAiB,EAAE,KAAc,EAAE,GAAY,EAAE,QAAwB;QACjL,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QAG5H,IAAI,uBAAuB,GAAG,IAAA,eAAI,EAAiB,qBAAqB,CAAC,CAAC;QAC1E,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;YAClD,uBAAuB,GAAG,uBAAuB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAClH;QAED,OAAO,uBAAuB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACxG,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,QAAyB,EAAE,EAAE;YACxC,IAAI,wBAAwB,GAAG,IAAA,aAAE,EAAC,QAAQ,CAAC,CAAC;YAC5C,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAClD,wBAAwB,GAAG,wBAAwB,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,GAAoB,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACtH;YACD,OAAO,wBAAwB,CAAC,IAAI,CAAC,IAAA,cAAG,EAAC,CAAC,GAAoB,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC3H,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;IAcM,SAAS,CAAC,MAAe,EAAE,IAAa,EAAE,GAAa,EAAE,GAAa,EAAE,MAAe,EAAE,QAAiB,EAAE,KAAc,EAAE,GAAY,EAAE,QAAwB;QACrK,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAA,cAAG,EAAC,CAAC,WAAgD,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACxL,CAAC;CAEJ;AA7GD,wEA6GC"}