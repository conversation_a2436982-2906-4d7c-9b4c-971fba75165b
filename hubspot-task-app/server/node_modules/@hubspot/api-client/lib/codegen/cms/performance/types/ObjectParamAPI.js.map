{"version": 3, "file": "ObjectParamAPI.js", "sourceRoot": "", "sources": ["../../../../../codegen/cms/performance/types/ObjectParamAPI.ts"], "names": [], "mappings": ";;;AAKA,mDAAiE;AAyGjE,MAAa,0BAA0B;IAGnC,YAAmB,aAA4B,EAAE,cAAmD,EAAE,iBAAyD;QAC3J,IAAI,CAAC,GAAG,GAAG,IAAI,8CAA8B,CAAC,aAAa,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;IACpG,CAAC;IAOM,mBAAmB,CAAC,QAA4C,EAAE,EAAE,OAAuB;QAC9F,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACpK,CAAC;IAOM,OAAO,CAAC,QAA4C,EAAE,EAAE,OAAuB;QAClF,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACxJ,CAAC;IAOM,qBAAqB,CAAC,QAA8C,EAAE,EAAE,OAAuB;QAClG,OAAO,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IACtK,CAAC;IAOM,SAAS,CAAC,QAA8C,EAAE,EAAE,OAAuB;QACtF,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAG,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAC1J,CAAC;CAEJ;AA3CD,gEA2CC"}