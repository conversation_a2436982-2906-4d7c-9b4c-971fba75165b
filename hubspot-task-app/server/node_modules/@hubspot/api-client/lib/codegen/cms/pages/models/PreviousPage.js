"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreviousPage = void 0;
class PreviousPage {
    static getAttributeTypeMap() {
        return PreviousPage.attributeTypeMap;
    }
    constructor() {
    }
}
exports.PreviousPage = PreviousPage;
PreviousPage.discriminator = undefined;
PreviousPage.mapping = undefined;
PreviousPage.attributeTypeMap = [
    {
        "name": "before",
        "baseName": "before",
        "type": "string",
        "format": ""
    },
    {
        "name": "link",
        "baseName": "link",
        "type": "string",
        "format": ""
    }
];
//# sourceMappingURL=PreviousPage.js.map